# Na Food - Website B<PERSON>ồ Ăn Trực Tuyến

## Tổng quan
Na Food là một website bán đồ ăn trực tuyến được xây dựng với kiến trúc SPA (Single Page Application).

## Công nghệ sử dụng
- **Frontend**: HTML, CSS, JavaScript (Vanilla JS)
- **Backend**: Node.js (Express.js)
- **Database**: MongoDB
- **Authentication**: JWT
- **Deployment**: Docker + Docker Compose

## Cấu trúc dự án
```
Na Food/
├── frontend/           # Giao diện người dùng
├── backend/           # API server
├── docker-compose.yml # Docker configuration
└── README.md         # Tài liệu dự án
```

## Phân quyền người dùng
- **User**: Đăng ký, đăng nhập, đặt hàng, xem/hủy đơn, đ<PERSON><PERSON> gi<PERSON> món
- **Staff**: <PERSON>uản lý đơn hàng
- **Admin**: T<PERSON>t cả quyền + q<PERSON><PERSON><PERSON> lý m<PERSON>, user, banner, thống kê

## Hướng dẫn chạy dự án
1. Clone repository
2. Chạy `docker-compose up -d`
3. Truy cập http://localhost:3000 (frontend)
4. API server: http://localhost:5000

## API Documentation
Xem chi tiết tại `/backend/API.md`
