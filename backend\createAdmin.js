const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// User schema (simplified)
const userSchema = new mongoose.Schema({
  name: String,
  email: String,
  password: String,
  role: String,
  isActive: Boolean
}, { timestamps: true });

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) {
    next();
  }
  const salt = await bcrypt.genSalt(10);
  this.password = await bcrypt.hash(this.password, salt);
});

const User = mongoose.model('User', userSchema);

async function createAdmin() {
  try {
    // Check if admin already exists
    const existingAdmin = await User.findOne({ email: '<EMAIL>' });
    
    if (existingAdmin) {
      console.log('Admin user already exists!');
      console.log('Email: <EMAIL>');
      console.log('Password: admin123');
      process.exit(0);
    }

    // Create admin user
    const admin = new User({
      name: 'Administrator',
      email: '<EMAIL>',
      password: 'admin123',
      role: 'admin',
      isActive: true
    });

    await admin.save();
    
    console.log('✅ Admin user created successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: admin123');
    console.log('👤 Role: admin');
    
    // Create some sample products
    const Product = mongoose.model('Product', new mongoose.Schema({
      name: String,
      description: String,
      price: Number,
      category: String,
      images: [{ url: String, alt: String, isPrimary: Boolean }],
      isAvailable: Boolean,
      isFeatured: Boolean,
      rating: { average: Number, count: Number },
      soldCount: Number,
      viewCount: Number,
      createdBy: { type: mongoose.Schema.ObjectId, ref: 'User' }
    }, { timestamps: true }));

    const sampleProducts = [
      {
        name: 'Phở Bò Tái',
        description: 'Phở bò tái truyền thống với nước dùng đậm đà, thịt bò tái mềm ngon',
        price: 65000,
        category: 'traditional',
        images: [{ url: '/images/pho-bo-tai.jpg', alt: 'Phở Bò Tái', isPrimary: true }],
        isAvailable: true,
        isFeatured: true,
        rating: { average: 4.5, count: 25 },
        soldCount: 150,
        viewCount: 500,
        createdBy: admin._id
      },
      {
        name: 'Bánh Mì Thịt Nướng',
        description: 'Bánh mì giòn với thịt nướng thơm lừng, rau sống tươi ngon',
        price: 25000,
        category: 'fast-food',
        images: [{ url: '/images/banh-mi.jpg', alt: 'Bánh Mì Thịt Nướng', isPrimary: true }],
        isAvailable: true,
        isFeatured: false,
        rating: { average: 4.2, count: 18 },
        soldCount: 89,
        viewCount: 320,
        createdBy: admin._id
      },
      {
        name: 'Cơm Tấm Sườn Nướng',
        description: 'Cơm tấm với sườn nướng đậm đà, chả trứng và bì',
        price: 45000,
        category: 'main-course',
        images: [{ url: '/images/com-tam.jpg', alt: 'Cơm Tấm Sườn Nướng', isPrimary: true }],
        isAvailable: true,
        isFeatured: true,
        rating: { average: 4.7, count: 32 },
        soldCount: 200,
        viewCount: 650,
        createdBy: admin._id
      }
    ];

    await Product.insertMany(sampleProducts);
    console.log('✅ Sample products created!');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error creating admin:', error);
    process.exit(1);
  }
}

createAdmin();
