// Payment Module
const Payment = {
    currentOrder: null,
    paymentStatus: 'pending',

    init(params = {}) {
        this.loadPaymentInfo(params);
        this.bindEvents();
    },

    bindEvents() {
        // Payment method selection
        const paymentMethods = document.querySelectorAll('input[name="payment-method"]');
        paymentMethods.forEach(method => {
            method.addEventListener('change', (e) => {
                this.selectPaymentMethod(e.target.value);
            });
        });

        // Payment buttons
        const payBtn = document.getElementById('pay-now-btn');
        if (payBtn) {
            payBtn.addEventListener('click', () => {
                this.processPayment();
            });
        }

        const backBtn = document.getElementById('back-to-orders-btn');
        if (backBtn) {
            backBtn.addEventListener('click', () => {
                App.showView('orders');
            });
        }

        const homeBtn = document.getElementById('back-to-home-btn');
        if (homeBtn) {
            homeBtn.addEventListener('click', () => {
                App.showView('home');
            });
        }
    },

    async loadPaymentInfo(params) {
        try {
            Loading.show();

            const { orderId, status } = params;
            
            if (orderId) {
                // Load order details
                const response = await API.get(`/orders/${orderId}`);
                this.currentOrder = response.data;
                this.paymentStatus = status || this.currentOrder.paymentStatus || 'pending';
            } else {
                // Mock order for demo
                this.currentOrder = {
                    _id: 'demo-order',
                    orderNumber: 'NF' + Date.now(),
                    total: 250000,
                    paymentMethod: 'cod',
                    items: [
                        {
                            name: 'Phở Bò Tái',
                            quantity: 2,
                            price: 65000,
                            image: '/images/pho-bo.jpg'
                        }
                    ],
                    deliveryInfo: {
                        name: 'Nguyễn Văn A',
                        phone: '0123456789',
                        address: '123 Đường ABC, Quận 1, TP.HCM'
                    }
                };
                this.paymentStatus = status || 'success';
            }

            this.renderPaymentPage();

        } catch (error) {
            console.error('Load payment info error:', error);
            this.showPaymentError('Không thể tải thông tin thanh toán');
        } finally {
            Loading.hide();
        }
    },

    renderPaymentPage() {
        const container = document.getElementById('payment-container');
        if (!container) return;

        switch (this.paymentStatus) {
            case 'success':
                this.renderPaymentSuccess();
                break;
            case 'failed':
                this.renderPaymentFailed();
                break;
            case 'pending':
            default:
                this.renderPaymentPending();
                break;
        }
    },

    renderPaymentSuccess() {
        const container = document.getElementById('payment-container');
        container.innerHTML = `
            <div class="payment-status">
                <div class="status-icon status-success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h1 class="status-title">Thanh toán thành công!</h1>
                <p class="status-message">
                    Cảm ơn bạn đã đặt hàng. Đơn hàng #${this.currentOrder.orderNumber} đã được xác nhận 
                    và sẽ được chuẩn bị trong thời gian sớm nhất.
                </p>
                
                <div class="payment-order-summary">
                    <h3>Thông tin đơn hàng</h3>
                    <div class="order-details">
                        <div class="detail-item">
                            <span class="detail-label">Mã đơn hàng:</span>
                            <span class="detail-value">#${this.currentOrder.orderNumber}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Tổng tiền:</span>
                            <span class="detail-value price">${Utils.formatCurrency(this.currentOrder.total)}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Phương thức thanh toán:</span>
                            <span class="detail-value">${this.getPaymentMethodText(this.currentOrder.paymentMethod)}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Giao đến:</span>
                            <span class="detail-value">${this.currentOrder.deliveryInfo.address}</span>
                        </div>
                    </div>
                </div>
                
                <div class="payment-actions">
                    <button id="back-to-orders-btn" class="payment-btn btn-primary">
                        <i class="fas fa-list"></i>
                        Xem đơn hàng
                    </button>
                    <button id="back-to-home-btn" class="payment-btn btn-secondary">
                        <i class="fas fa-home"></i>
                        Về trang chủ
                    </button>
                </div>
                
                <div class="security-notice">
                    <i class="fas fa-shield-alt"></i>
                    <p>Thông tin thanh toán của bạn được bảo mật tuyệt đối</p>
                </div>
            </div>
        `;
        
        this.bindEvents();
    },

    renderPaymentFailed() {
        const container = document.getElementById('payment-container');
        container.innerHTML = `
            <div class="payment-status">
                <div class="status-icon status-failed">
                    <i class="fas fa-times-circle"></i>
                </div>
                <h1 class="status-title">Thanh toán thất bại!</h1>
                <p class="status-message">
                    Rất tiếc, thanh toán cho đơn hàng #${this.currentOrder.orderNumber} không thành công. 
                    Vui lòng thử lại hoặc chọn phương thức thanh toán khác.
                </p>
                
                <div class="payment-order-summary">
                    <h3>Thông tin đơn hàng</h3>
                    <div class="order-details">
                        <div class="detail-item">
                            <span class="detail-label">Mã đơn hàng:</span>
                            <span class="detail-value">#${this.currentOrder.orderNumber}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Tổng tiền:</span>
                            <span class="detail-value price">${Utils.formatCurrency(this.currentOrder.total)}</span>
                        </div>
                    </div>
                </div>
                
                <div class="payment-actions">
                    <button onclick="Payment.retryPayment()" class="payment-btn btn-primary">
                        <i class="fas fa-redo"></i>
                        Thử lại thanh toán
                    </button>
                    <button id="back-to-home-btn" class="payment-btn btn-secondary">
                        <i class="fas fa-home"></i>
                        Về trang chủ
                    </button>
                </div>
            </div>
        `;
        
        this.bindEvents();
    },

    renderPaymentPending() {
        const container = document.getElementById('payment-container');
        container.innerHTML = `
            <div class="payment-header">
                <h1>Thanh toán đơn hàng</h1>
                <p>Hoàn tất thanh toán để xác nhận đơn hàng #${this.currentOrder.orderNumber}</p>
            </div>
            
            <div class="payment-order-summary">
                <h3>Thông tin đơn hàng</h3>
                <div class="order-details">
                    <div class="detail-item">
                        <span class="detail-label">Mã đơn hàng:</span>
                        <span class="detail-value">#${this.currentOrder.orderNumber}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Tổng tiền:</span>
                        <span class="detail-value price">${Utils.formatCurrency(this.currentOrder.total)}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Giao đến:</span>
                        <span class="detail-value">${this.currentOrder.deliveryInfo.name} - ${this.currentOrder.deliveryInfo.phone}</span>
                    </div>
                </div>
                
                <div class="order-items">
                    <h4>Món ăn đã đặt</h4>
                    ${this.currentOrder.items.map(item => `
                        <div class="order-item">
                            <img src="${item.image}" alt="${item.name}" class="item-image">
                            <div class="item-info">
                                <div class="item-name">${item.name}</div>
                                <div class="item-quantity">Số lượng: ${item.quantity}</div>
                            </div>
                            <div class="item-price">${Utils.formatCurrency(item.price * item.quantity)}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
            
            <div class="payment-methods-section">
                <h3>Chọn phương thức thanh toán</h3>
                <div class="payment-options">
                    <div class="payment-option method-cod ${this.currentOrder.paymentMethod === 'cod' ? 'selected' : ''}">
                        <input type="radio" name="payment-method" value="cod" ${this.currentOrder.paymentMethod === 'cod' ? 'checked' : ''}>
                        <div class="payment-method-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="payment-method-info">
                            <h4>Tiền mặt (COD)</h4>
                            <p>Thanh toán khi nhận hàng</p>
                        </div>
                        <div class="payment-method-fee">Miễn phí</div>
                    </div>
                    
                    <div class="payment-option method-bank">
                        <input type="radio" name="payment-method" value="bank_transfer">
                        <div class="payment-method-icon">
                            <i class="fas fa-university"></i>
                        </div>
                        <div class="payment-method-info">
                            <h4>Chuyển khoản ngân hàng</h4>
                            <p>Chuyển khoản qua ATM/Internet Banking</p>
                        </div>
                        <div class="payment-method-fee">Miễn phí</div>
                    </div>
                    
                    <div class="payment-option method-ewallet">
                        <input type="radio" name="payment-method" value="e_wallet">
                        <div class="payment-method-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <div class="payment-method-info">
                            <h4>Ví điện tử</h4>
                            <p>MoMo, ZaloPay, ViettelPay</p>
                        </div>
                        <div class="payment-method-fee">Miễn phí</div>
                    </div>
                </div>
            </div>
            
            <!-- Bank Transfer Form -->
            <div id="bank_transfer-form" class="payment-form">
                <h4>Thông tin chuyển khoản</h4>
                <div class="bank-info">
                    <h5>Thông tin tài khoản nhận</h5>
                    <div class="bank-details">
                        <div class="bank-detail">
                            <span class="bank-label">Ngân hàng:</span>
                            <span class="bank-value">Vietcombank</span>
                        </div>
                        <div class="bank-detail">
                            <span class="bank-label">Số tài khoản:</span>
                            <span class="bank-value">**********</span>
                            <button class="copy-btn" onclick="Utils.copyToClipboard('**********')">Copy</button>
                        </div>
                        <div class="bank-detail">
                            <span class="bank-label">Chủ tài khoản:</span>
                            <span class="bank-value">CONG TY NA FOOD</span>
                        </div>
                        <div class="bank-detail">
                            <span class="bank-label">Nội dung:</span>
                            <span class="bank-value">NF${this.currentOrder.orderNumber}</span>
                            <button class="copy-btn" onclick="Utils.copyToClipboard('NF${this.currentOrder.orderNumber}')">Copy</button>
                        </div>
                    </div>
                </div>
                
                <div class="qr-section">
                    <h5>Quét mã QR để chuyển khoản</h5>
                    <div class="qr-code">
                        <div class="qr-placeholder">
                            <i class="fas fa-qrcode"></i>
                        </div>
                    </div>
                    <p>Quét mã QR bằng app ngân hàng để chuyển khoản nhanh chóng</p>
                </div>
            </div>
            
            <div class="payment-actions">
                <button id="pay-now-btn" class="payment-btn btn-pay">
                    <i class="fas fa-credit-card"></i>
                    Xác nhận thanh toán
                </button>
                <button id="back-to-home-btn" class="payment-btn btn-cancel">
                    <i class="fas fa-arrow-left"></i>
                    Quay lại
                </button>
            </div>
            
            <div class="security-notice">
                <i class="fas fa-shield-alt"></i>
                <p>Thông tin thanh toán của bạn được bảo mật tuyệt đối</p>
            </div>
        `;
        
        this.bindEvents();
    },

    selectPaymentMethod(method) {
        // Update selected payment method
        document.querySelectorAll('.payment-option').forEach(option => {
            option.classList.remove('selected');
        });
        
        const selectedOption = document.querySelector(`input[value="${method}"]`)?.closest('.payment-option');
        if (selectedOption) {
            selectedOption.classList.add('selected');
        }

        // Show/hide payment forms
        document.querySelectorAll('.payment-form').forEach(form => {
            form.classList.remove('active');
        });
        
        const activeForm = document.getElementById(`${method}-form`);
        if (activeForm) {
            activeForm.classList.add('active');
        }

        // Update current order payment method
        if (this.currentOrder) {
            this.currentOrder.paymentMethod = method;
        }
    },

    async processPayment() {
        try {
            Loading.show();

            const selectedMethod = document.querySelector('input[name="payment-method"]:checked')?.value;
            
            if (!selectedMethod) {
                Toast.error('Vui lòng chọn phương thức thanh toán');
                return;
            }

            // Mock payment processing
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Simulate payment result
            const isSuccess = Math.random() > 0.1; // 90% success rate

            if (isSuccess) {
                this.paymentStatus = 'success';
                Toast.success('Thanh toán thành công!');
            } else {
                this.paymentStatus = 'failed';
                Toast.error('Thanh toán thất bại!');
            }

            this.renderPaymentPage();

        } catch (error) {
            console.error('Process payment error:', error);
            Toast.error('Có lỗi xảy ra trong quá trình thanh toán');
        } finally {
            Loading.hide();
        }
    },

    async retryPayment() {
        this.paymentStatus = 'pending';
        this.renderPaymentPage();
    },

    getPaymentMethodText(method) {
        const methodMap = {
            'cod': 'Tiền mặt (COD)',
            'bank_transfer': 'Chuyển khoản ngân hàng',
            'e_wallet': 'Ví điện tử',
            'credit_card': 'Thẻ tín dụng'
        };
        return methodMap[method] || method;
    },

    showPaymentError(message) {
        const container = document.getElementById('payment-container');
        if (container) {
            container.innerHTML = `
                <div class="payment-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>Có lỗi xảy ra</h3>
                    <p>${message}</p>
                    <button onclick="App.showView('home')" class="btn btn-primary">Về trang chủ</button>
                </div>
            `;
        }
    }
};

// Export for global use
window.Payment = Payment;
