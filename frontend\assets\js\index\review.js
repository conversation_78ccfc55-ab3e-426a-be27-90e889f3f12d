// Review Module
const Review = {
    currentProductId: null,
    selectedRating: 0,

    init() {
        this.bindEvents();
    },

    bindEvents() {
        // Write review button
        const writeReviewBtn = document.getElementById('write-review-btn');
        if (writeReviewBtn) {
            writeReviewBtn.addEventListener('click', () => {
                this.showReviewForm();
            });
        }

        // Review filters
        const filterSelect = document.getElementById('review-filter');
        if (filterSelect) {
            filterSelect.addEventListener('change', (e) => {
                this.filterReviews(e.target.value);
            });
        }

        // Sort reviews
        const sortSelect = document.getElementById('review-sort');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.sortReviews(e.target.value);
            });
        }
    },

    showReviewForm(productId = null) {
        // Check if user is logged in
        const token = Storage.get('token');
        if (!token) {
            Toast.error('Vui lòng đăng nhập để viết đánh giá');
            Auth.showLoginModal();
            return;
        }

        this.currentProductId = productId;

        const modalContent = `
            <div class="review-modal">
                <div class="modal-header">
                    <h3>Viết đánh giá</h3>
                    <button class="modal-close" onclick="Modal.hide()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="review-form" class="review-form">
                        <div class="form-group">
                            <label>Đánh giá *</label>
                            <div class="rating-input" id="rating-input">
                                ${[1,2,3,4,5].map(i => `
                                    <i class="star-input far fa-star" data-rating="${i}" onclick="Review.selectRating(${i})"></i>
                                `).join('')}
                            </div>
                            <div class="rating-text" id="rating-text">Chọn số sao</div>
                        </div>
                        
                        <div class="form-group">
                            <label>Tiêu đề</label>
                            <input type="text" name="title" placeholder="Tóm tắt đánh giá của bạn">
                        </div>
                        
                        <div class="form-group">
                            <label>Nội dung *</label>
                            <textarea name="comment" rows="4" placeholder="Chia sẻ trải nghiệm của bạn về sản phẩm này..." required></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label>Hình ảnh (tùy chọn)</label>
                            <div class="image-upload" onclick="document.getElementById('review-images').click()">
                                <input type="file" id="review-images" multiple accept="image/*" style="display: none;">
                                <div class="upload-text">
                                    <i class="fas fa-camera"></i>
                                    <span>Thêm hình ảnh</span>
                                </div>
                                <div class="upload-hint">Tối đa 5 hình ảnh, mỗi hình dưới 5MB</div>
                            </div>
                            <div id="image-preview" class="image-preview"></div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="form-btn btn-submit">Gửi đánh giá</button>
                            <button type="button" class="form-btn btn-cancel" onclick="Modal.hide()">Hủy</button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        Modal.show(modalContent);

        // Bind form submit
        document.getElementById('review-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitReview(e.target);
        });

        // Bind image upload
        document.getElementById('review-images').addEventListener('change', (e) => {
            this.handleImageUpload(e.target.files);
        });
    },

    selectRating(rating) {
        this.selectedRating = rating;
        this.updateStarDisplay(rating);
        
        const ratingTexts = {
            1: 'Rất tệ',
            2: 'Tệ',
            3: 'Bình thường',
            4: 'Tốt',
            5: 'Rất tốt'
        };
        
        document.getElementById('rating-text').textContent = ratingTexts[rating];
    },

    updateStarDisplay(rating) {
        const stars = document.querySelectorAll('.star-input');
        stars.forEach((star, index) => {
            if (index < rating) {
                star.classList.remove('far');
                star.classList.add('fas');
                star.style.color = '#ffc107';
            } else {
                star.classList.remove('fas');
                star.classList.add('far');
                star.style.color = '#ddd';
            }
        });
    },

    handleImageUpload(files) {
        const preview = document.getElementById('image-preview');
        const maxFiles = 5;
        const maxSize = 5 * 1024 * 1024; // 5MB

        if (files.length > maxFiles) {
            Toast.error(`Chỉ được chọn tối đa ${maxFiles} hình ảnh`);
            return;
        }

        preview.innerHTML = '';

        Array.from(files).forEach((file, index) => {
            if (file.size > maxSize) {
                Toast.error(`Hình ảnh ${file.name} quá lớn (tối đa 5MB)`);
                return;
            }

            if (!file.type.startsWith('image/')) {
                Toast.error(`${file.name} không phải là hình ảnh`);
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                const imageDiv = document.createElement('div');
                imageDiv.className = 'preview-image';
                imageDiv.innerHTML = `
                    <img src="${e.target.result}" alt="Preview ${index + 1}">
                    <button type="button" class="remove-image" onclick="this.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                preview.appendChild(imageDiv);
            };
            reader.readAsDataURL(file);
        });
    },

    async submitReview(form) {
        try {
            if (!this.selectedRating) {
                Toast.error('Vui lòng chọn số sao đánh giá');
                return;
            }

            const formData = new FormData(form);
            const reviewData = {
                productId: this.currentProductId,
                rating: this.selectedRating,
                title: formData.get('title'),
                comment: formData.get('comment')
            };

            if (!reviewData.comment.trim()) {
                Toast.error('Vui lòng nhập nội dung đánh giá');
                return;
            }

            Loading.show();

            // Mock API call - replace with real endpoint
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            Modal.hide();
            Toast.success('Cảm ơn bạn đã đánh giá sản phẩm!');
            
            // Reload reviews if on product page
            if (typeof ProductDetail !== 'undefined' && ProductDetail.loadReviews) {
                ProductDetail.loadReviews();
            }

        } catch (error) {
            console.error('Submit review error:', error);
            Toast.error('Có lỗi xảy ra khi gửi đánh giá');
        } finally {
            Loading.hide();
        }
    },

    async loadReviews(productId, filters = {}) {
        try {
            Loading.show();

            // Mock reviews data
            const mockReviews = [
                {
                    _id: '1',
                    user: { name: 'Nguyễn Văn A', avatar: null },
                    rating: 5,
                    title: 'Rất ngon!',
                    comment: 'Món ăn rất ngon, phục vụ tốt. Sẽ đặt lại lần sau.',
                    images: [],
                    helpfulCount: 12,
                    isHelpful: false,
                    isVerifiedPurchase: true,
                    createdAt: new Date('2024-11-20')
                },
                {
                    _id: '2',
                    user: { name: 'Trần Thị B', avatar: null },
                    rating: 4,
                    title: 'Khá ổn',
                    comment: 'Món ăn ngon, giá cả hợp lý. Giao hàng nhanh.',
                    images: [],
                    helpfulCount: 8,
                    isHelpful: true,
                    isVerifiedPurchase: true,
                    createdAt: new Date('2024-11-19')
                },
                {
                    _id: '3',
                    user: { name: 'Lê Văn C', avatar: null },
                    rating: 3,
                    title: 'Bình thường',
                    comment: 'Món ăn ổn, không có gì đặc biệt. Có thể cải thiện thêm.',
                    images: [],
                    helpfulCount: 3,
                    isHelpful: false,
                    isVerifiedPurchase: false,
                    createdAt: new Date('2024-11-18')
                }
            ];

            this.renderReviews(mockReviews);

        } catch (error) {
            console.error('Load reviews error:', error);
            Toast.error('Không thể tải đánh giá');
        } finally {
            Loading.hide();
        }
    },

    renderReviews(reviews) {
        const container = document.getElementById('reviews-list');
        if (!container) return;

        if (reviews.length === 0) {
            container.innerHTML = `
                <div class="empty-reviews">
                    <i class="fas fa-star"></i>
                    <h4>Chưa có đánh giá nào</h4>
                    <p>Hãy là người đầu tiên đánh giá sản phẩm này!</p>
                </div>
            `;
            return;
        }

        container.innerHTML = reviews.map(review => `
            <div class="review-item">
                <div class="review-header">
                    <div class="reviewer-info">
                        <div class="reviewer-avatar">
                            ${review.user.avatar ? 
                                `<img src="${review.user.avatar}" alt="${review.user.name}">` :
                                `<span>${review.user.name.charAt(0).toUpperCase()}</span>`
                            }
                        </div>
                        <div class="reviewer-details">
                            <h5>${review.user.name}</h5>
                            <div class="review-rating">${this.renderStars(review.rating)}</div>
                            <div class="review-date">${Utils.formatDate(review.createdAt)}</div>
                        </div>
                    </div>
                    <div class="review-actions">
                        <button class="action-btn btn-helpful ${review.isHelpful ? 'active' : ''}" 
                                onclick="Review.toggleHelpful('${review._id}')">
                            <i class="fas fa-thumbs-up"></i>
                            <span>${review.helpfulCount}</span>
                        </button>
                        <button class="action-btn btn-report" onclick="Review.reportReview('${review._id}')">
                            <i class="fas fa-flag"></i>
                        </button>
                    </div>
                </div>
                
                <div class="review-content">
                    ${review.title ? `<div class="review-title">${review.title}</div>` : ''}
                    <div class="review-text">${review.comment}</div>
                    ${review.images && review.images.length > 0 ? `
                        <div class="review-images">
                            ${review.images.map(img => `
                                <img src="${img}" alt="Review image" class="review-image" 
                                     onclick="Review.showImageModal('${img}')">
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
                
                <div class="review-footer">
                    <div class="helpful-count">
                        ${review.helpfulCount} người thấy hữu ích
                    </div>
                    ${review.isVerifiedPurchase ? `
                        <div class="verified-purchase">
                            <i class="fas fa-check-circle"></i>
                            Đã mua hàng
                        </div>
                    ` : ''}
                </div>
            </div>
        `).join('');
    },

    renderStars(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

        let starsHTML = '';
        
        for (let i = 0; i < fullStars; i++) {
            starsHTML += '<i class="fas fa-star"></i>';
        }
        
        if (hasHalfStar) {
            starsHTML += '<i class="fas fa-star-half-alt"></i>';
        }
        
        for (let i = 0; i < emptyStars; i++) {
            starsHTML += '<i class="far fa-star"></i>';
        }

        return starsHTML;
    },

    async toggleHelpful(reviewId) {
        try {
            // Check if user is logged in
            if (!Auth.isLoggedIn()) {
                Toast.error('Vui lòng đăng nhập để đánh giá hữu ích');
                return;
            }

            // Mock API call
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Update UI
            const button = document.querySelector(`button[onclick="Review.toggleHelpful('${reviewId}')"]`);
            if (button) {
                const isActive = button.classList.contains('active');
                const countSpan = button.querySelector('span');
                let count = parseInt(countSpan.textContent);
                
                if (isActive) {
                    button.classList.remove('active');
                    count--;
                    Toast.info('Đã bỏ đánh giá hữu ích');
                } else {
                    button.classList.add('active');
                    count++;
                    Toast.success('Cảm ơn phản hồi của bạn!');
                }
                
                countSpan.textContent = count;
            }

        } catch (error) {
            console.error('Toggle helpful error:', error);
            Toast.error('Có lỗi xảy ra');
        }
    },

    async reportReview(reviewId) {
        if (!Auth.isLoggedIn()) {
            Toast.error('Vui lòng đăng nhập để báo cáo');
            return;
        }

        if (confirm('Bạn có chắc chắn muốn báo cáo đánh giá này?')) {
            try {
                // Mock API call
                await new Promise(resolve => setTimeout(resolve, 500));
                Toast.success('Đã gửi báo cáo. Chúng tôi sẽ xem xét sớm nhất.');
            } catch (error) {
                console.error('Report review error:', error);
                Toast.error('Có lỗi xảy ra khi gửi báo cáo');
            }
        }
    },

    showImageModal(imageSrc) {
        const modalContent = `
            <div class="image-modal">
                <div class="modal-header">
                    <h3>Hình ảnh đánh giá</h3>
                    <button class="modal-close" onclick="Modal.hide()">&times;</button>
                </div>
                <div class="modal-body">
                    <img src="${imageSrc}" alt="Review image" class="modal-image">
                </div>
            </div>
        `;
        Modal.show(modalContent);
    },

    filterReviews(filter) {
        // Mock filter implementation
        Toast.info(`Lọc đánh giá: ${filter}`);
    },

    sortReviews(sort) {
        // Mock sort implementation
        Toast.info(`Sắp xếp: ${sort}`);
    },

    // Calculate review statistics
    calculateStats(reviews) {
        if (!reviews || reviews.length === 0) {
            return {
                average: 0,
                total: 0,
                breakdown: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 }
            };
        }

        const total = reviews.length;
        const sum = reviews.reduce((acc, review) => acc + review.rating, 0);
        const average = sum / total;

        const breakdown = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 };
        reviews.forEach(review => {
            breakdown[review.rating]++;
        });

        return { average, total, breakdown };
    },

    renderReviewStats(stats) {
        const container = document.getElementById('review-stats');
        if (!container) return;

        container.innerHTML = `
            <div class="rating-summary">
                <div class="rating-average">${stats.average.toFixed(1)}</div>
                <div class="rating-stars">${this.renderStars(stats.average)}</div>
                <div class="rating-count">${stats.total} đánh giá</div>
            </div>
            
            <div class="rating-breakdown">
                ${[5,4,3,2,1].map(star => `
                    <div class="rating-bar">
                        <div class="bar-label">
                            <span>${star}</span>
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="bar-progress">
                            <div class="bar-fill" style="width: ${stats.total > 0 ? (stats.breakdown[star] / stats.total * 100) : 0}%"></div>
                        </div>
                        <div class="bar-count">${stats.breakdown[star]}</div>
                    </div>
                `).join('')}
            </div>
        `;
    }
};

// Export for global use
window.Review = Review;
