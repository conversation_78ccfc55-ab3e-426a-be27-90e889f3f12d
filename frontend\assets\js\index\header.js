// Header Module for Na Food
const HeaderModule = {
    currentUser: null,
    cartCount: 0,

    init() {
        this.bindEvents();
        this.checkAuthStatus();
        this.updateCartCount();
        this.initSearch();
    },

    bindEvents() {
        // Navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.getAttribute('data-page');
                if (page) {
                    this.navigateToPage(page);
                }
            });
        });

        // Cart icon click
        const cartIcon = document.getElementById('cart-icon');
        if (cartIcon) {
            cartIcon.addEventListener('click', () => {
                this.navigateToPage('cart');
            });
        }

        // User menu actions
        const profileLink = document.getElementById('profile-link');
        const ordersLink = document.getElementById('orders-link');
        const logoutLink = document.getElementById('logout-link');

        if (profileLink) {
            profileLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.showProfile();
            });
        }

        if (ordersLink) {
            ordersLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.navigateToPage('orders');
            });
        }

        if (logoutLink) {
            logoutLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.logout();
            });
        }

        // Mobile menu toggle
        const mobileToggle = document.getElementById('mobile-menu-toggle');
        if (mobileToggle) {
            mobileToggle.addEventListener('click', () => {
                this.toggleMobileMenu();
            });
        }

        // Search functionality
        const searchBtn = document.getElementById('search-btn');
        const searchInput = document.getElementById('search-input');

        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.performSearch();
            });
        }

        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.performSearch();
                }
            });
        }
    },

    async checkAuthStatus() {
        const token = Storage.get('token');
        if (token) {
            try {
                const response = await API.get('/auth/me');
                this.currentUser = response.data;
                this.updateUserDisplay();
            } catch (error) {
                console.error('Auth check failed:', error);
                Storage.remove('token');
                this.updateUserDisplay();
            }
        } else {
            this.updateUserDisplay();
        }
    },

    updateUserDisplay() {
        const userNameElement = document.getElementById('user-name');
        const userDropdown = document.getElementById('user-dropdown');

        if (this.currentUser) {
            userNameElement.textContent = this.currentUser.name;
            userDropdown.style.display = 'block';
        } else {
            userNameElement.textContent = 'Đăng nhập';
            userDropdown.style.display = 'none';
            
            // Add click event to show login modal
            const userInfo = document.getElementById('user-info');
            if (userInfo) {
                userInfo.addEventListener('click', () => {
                    this.showAuthModal();
                });
            }
        }
    },

    updateCartCount() {
        const cart = Storage.get('cart') || [];
        this.cartCount = cart.reduce((total, item) => total + item.quantity, 0);
        
        const cartCountElement = document.getElementById('cart-count');
        if (cartCountElement) {
            cartCountElement.textContent = this.cartCount;
            cartCountElement.style.display = this.cartCount > 0 ? 'flex' : 'none';
        }
    },

    navigateToPage(pageName) {
        // Hide all pages
        document.querySelectorAll('.page').forEach(page => {
            page.classList.remove('active');
        });

        // Show target page
        const targetPage = document.getElementById(`${pageName}-page`);
        if (targetPage) {
            targetPage.classList.add('active');
        }

        // Update active nav link
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });

        const activeLink = document.querySelector(`[data-page="${pageName}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }

        // Update URL hash
        window.location.hash = pageName;

        // Scroll to top
        window.scrollTo(0, 0);

        // Load page content if needed
        this.loadPageContent(pageName);
    },

    async loadPageContent(pageName) {
        switch (pageName) {
            case 'home':
                if (typeof ProductsModule !== 'undefined') {
                    await ProductsModule.loadFeaturedProducts();
                    await ProductsModule.loadCategories();
                }
                break;
            case 'products':
                if (typeof ProductsModule !== 'undefined') {
                    await ProductsModule.loadProducts();
                }
                break;
            case 'cart':
                if (typeof CartModule !== 'undefined') {
                    CartModule.displayCart();
                }
                break;
            case 'orders':
                if (this.currentUser && typeof OrdersModule !== 'undefined') {
                    await OrdersModule.loadOrders();
                } else {
                    this.showAuthModal();
                }
                break;
        }
    },

    initSearch() {
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            // Debounced search
            const debouncedSearch = Utils.debounce(() => {
                this.performSearch();
            }, 500);

            searchInput.addEventListener('input', debouncedSearch);
        }
    },

    performSearch() {
        const searchInput = document.getElementById('search-input');
        const query = searchInput.value.trim();

        if (query.length > 0) {
            // Navigate to products page with search
            this.navigateToPage('products');
            
            // Trigger search in products module
            if (typeof ProductsModule !== 'undefined') {
                ProductsModule.searchProducts(query);
            }
        }
    },

    showProfile() {
        if (!this.currentUser) {
            this.showAuthModal();
            return;
        }

        const profileContent = `
            <div class="profile-modal">
                <div class="modal-header">
                    <h2>Thông tin cá nhân</h2>
                    <button class="modal-close" onclick="Modal.hide()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="profile-form">
                        <div class="form-group">
                            <label>Tên</label>
                            <input type="text" name="name" value="${this.currentUser.name}" required>
                        </div>
                        <div class="form-group">
                            <label>Email</label>
                            <input type="email" value="${this.currentUser.email}" disabled>
                        </div>
                        <div class="form-group">
                            <label>Số điện thoại</label>
                            <input type="tel" name="phone" value="${this.currentUser.phone || ''}">
                        </div>
                        <div class="form-group">
                            <label>Địa chỉ</label>
                            <textarea name="address" rows="3">${this.currentUser.fullAddress || ''}</textarea>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Cập nhật</button>
                            <button type="button" class="btn btn-secondary" onclick="Modal.hide()">Hủy</button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        Modal.show(profileContent);

        // Handle profile form submission
        document.getElementById('profile-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.updateProfile(new FormData(e.target));
        });
    },

    async updateProfile(formData) {
        try {
            Loading.show();
            
            const profileData = {
                name: formData.get('name'),
                phone: formData.get('phone'),
                address: {
                    street: formData.get('address')
                }
            };

            const response = await API.put('/auth/profile', profileData);
            this.currentUser = response.data;
            this.updateUserDisplay();
            
            Modal.hide();
            Toast.success('Cập nhật thông tin thành công!');
        } catch (error) {
            Toast.error('Cập nhật thông tin thất bại: ' + error.message);
        } finally {
            Loading.hide();
        }
    },

    showAuthModal() {
        const authContent = `
            <div class="auth-modal">
                <div class="modal-header">
                    <h2>Đăng nhập</h2>
                    <button class="modal-close" onclick="Modal.hide()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="auth-tabs">
                        <button class="auth-tab active" data-tab="login">Đăng nhập</button>
                        <button class="auth-tab" data-tab="register">Đăng ký</button>
                    </div>
                    
                    <form id="login-form" class="auth-form active">
                        <div class="form-group">
                            <label>Email</label>
                            <input type="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label>Mật khẩu</label>
                            <input type="password" name="password" required>
                        </div>
                        <button type="submit" class="btn btn-primary btn-full">Đăng nhập</button>
                    </form>
                    
                    <form id="register-form" class="auth-form">
                        <div class="form-group">
                            <label>Tên</label>
                            <input type="text" name="name" required>
                        </div>
                        <div class="form-group">
                            <label>Email</label>
                            <input type="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label>Mật khẩu</label>
                            <input type="password" name="password" required>
                        </div>
                        <div class="form-group">
                            <label>Xác nhận mật khẩu</label>
                            <input type="password" name="confirmPassword" required>
                        </div>
                        <button type="submit" class="btn btn-primary btn-full">Đăng ký</button>
                    </form>
                </div>
            </div>
        `;

        Modal.show(authContent);

        // Initialize auth modal functionality
        if (typeof AuthModule !== 'undefined') {
            AuthModule.initModal();
        }
    },

    async logout() {
        try {
            await API.post('/auth/logout');
        } catch (error) {
            console.error('Logout error:', error);
        } finally {
            Storage.remove('token');
            this.currentUser = null;
            this.updateUserDisplay();
            this.navigateToPage('home');
            Toast.success('Đăng xuất thành công!');
        }
    },

    toggleMobileMenu() {
        const navMenu = document.querySelector('.nav-menu');
        if (navMenu) {
            navMenu.classList.toggle('mobile-active');
        }
    }
};

// Initialize on page load
window.HeaderModule = HeaderModule;
