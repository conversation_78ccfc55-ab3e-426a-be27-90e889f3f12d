const mongoose = require('mongoose');
const Product = require('./models/Product');
const User = require('./models/User');
require('dotenv').config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    console.log('MongoDB connected successfully');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// Sample products data
const getSampleProducts = (adminUserId) => [
  {
    name: 'Phở Bò Tái',
    description: 'Phở bò tái truyền thống với nước dùng đậm đà, thịt bò tái mềm ngọt',
    price: 65000,
    originalPrice: 65000,
    category: 'traditional',
    images: [
      {
        url: 'https://images.unsplash.com/photo-1555126634-323283e090fa?w=500',
        alt: 'Phở Bò Tái',
        isPrimary: true
      },
      {
        url: 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=500',
        alt: 'Phở Bò Tái 2',
        isPrimary: false
      }
    ],
    ingredients: ['Bánh phở', 'Thịt bò tái', 'Hành lá', 'Ngò gai', 'Giá đỗ'],
    isAvailable: true,
    isFeatured: true,
    preparationTime: 15,
    nutritionInfo: {
      calories: 350,
      protein: 25,
      carbs: 45,
      fat: 8
    },
    createdBy: adminUserId
  },
  {
    name: 'Bún Bò Huế',
    description: 'Bún bò Huế cay nồng đặc trưng miền Trung với chả cua, giò heo',
    price: 70000,
    category: 'Bún',
    images: [
      'https://images.unsplash.com/photo-1559847844-d721426d6edc?w=500'
    ],
    ingredients: ['Bún', 'Thịt bò', 'Chả cua', 'Giò heo', 'Tôm khô'],
    isAvailable: true,
    isFeatured: true,
    preparationTime: 20,
    nutrition: {
      calories: 420,
      protein: 30,
      carbs: 50,
      fat: 12
    }
  },
  {
    name: 'Cơm Tấm Sườn Nướng',
    description: 'Cơm tấm sườn nướng thơm lừng với chả trứng, bì và nước mắm pha',
    price: 75000,
    salePrice: 65000,
    category: 'Cơm',
    images: [
      'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=500'
    ],
    ingredients: ['Cơm tấm', 'Sườn nướng', 'Chả trứng', 'Bì', 'Dưa leo'],
    isAvailable: true,
    isFeatured: false,
    preparationTime: 25,
    nutrition: {
      calories: 580,
      protein: 35,
      carbs: 65,
      fat: 18
    }
  },
  {
    name: 'Bánh Mì Thịt Nướng',
    description: 'Bánh mì Việt Nam với thịt nướng, pate, rau thơm và nước sốt đặc biệt',
    price: 35000,
    category: 'Bánh Mì',
    images: [
      'https://images.unsplash.com/photo-1558030006-450675393462?w=500'
    ],
    ingredients: ['Bánh mì', 'Thịt nướng', 'Pate', 'Rau thơm', 'Dưa chua'],
    isAvailable: true,
    isFeatured: true,
    preparationTime: 10,
    nutrition: {
      calories: 320,
      protein: 20,
      carbs: 35,
      fat: 12
    }
  },
  {
    name: 'Gỏi Cuốn Tôm Thịt',
    description: 'Gỏi cuốn tươi mát với tôm, thịt heo, bún và rau sống, chấm nước mắm',
    price: 45000,
    category: 'Khai Vị',
    images: [
      'https://images.unsplash.com/photo-1559314809-0f31657def5e?w=500'
    ],
    ingredients: ['Bánh tráng', 'Tôm', 'Thịt heo', 'Bún', 'Rau sống'],
    isAvailable: true,
    isFeatured: false,
    preparationTime: 15,
    nutrition: {
      calories: 180,
      protein: 15,
      carbs: 20,
      fat: 5
    }
  },
  {
    name: 'Chả Cá Lã Vọng',
    description: 'Chả cá Hà Nội truyền thống với nghệ, thì là và bún',
    price: 85000,
    category: 'Đặc Sản',
    images: [
      'https://images.unsplash.com/photo-1565958011703-44f9829ba187?w=500'
    ],
    ingredients: ['Cá', 'Nghệ', 'Thì là', 'Bún', 'Đậu phộng'],
    isAvailable: true,
    isFeatured: true,
    preparationTime: 30,
    nutrition: {
      calories: 450,
      protein: 40,
      carbs: 25,
      fat: 20
    }
  },
  {
    name: 'Bún Chả Hà Nội',
    description: 'Bún chả Hà Nội với thịt nướng thơm phức, nước mắm chua ngọt',
    price: 60000,
    category: 'Bún',
    images: [
      'https://images.unsplash.com/photo-1569562211093-4ed0d0758f12?w=500'
    ],
    ingredients: ['Bún', 'Thịt nướng', 'Chả', 'Rau thơm', 'Nước mắm'],
    isAvailable: true,
    isFeatured: false,
    preparationTime: 20,
    nutrition: {
      calories: 380,
      protein: 28,
      carbs: 40,
      fat: 12
    }
  },
  {
    name: 'Hủ Tiếu Nam Vang',
    description: 'Hủ tiếu Nam Vang với tôm, thịt, gan và nước dùng trong vắt',
    price: 55000,
    category: 'Hủ Tiếu',
    images: [
      'https://images.unsplash.com/photo-1551218808-94e220e084d2?w=500'
    ],
    ingredients: ['Hủ tiếu', 'Tôm', 'Thịt heo', 'Gan', 'Hành lá'],
    isAvailable: true,
    isFeatured: false,
    preparationTime: 18,
    nutrition: {
      calories: 340,
      protein: 22,
      carbs: 42,
      fat: 8
    }
  },
  {
    name: 'Cao Lầu Hội An',
    description: 'Cao lầu đặc sản Hội An với mì đặc biệt, thịt xá xíu và rau thơm',
    price: 50000,
    category: 'Đặc Sản',
    images: [
      'https://images.unsplash.com/photo-1569562211093-4ed0d0758f12?w=500'
    ],
    ingredients: ['Mì cao lầu', 'Thịt xá xíu', 'Giá đỗ', 'Rau thơm', 'Bánh tráng'],
    isAvailable: true,
    isFeatured: true,
    preparationTime: 15,
    nutrition: {
      calories: 360,
      protein: 20,
      carbs: 45,
      fat: 10
    }
  },
  {
    name: 'Chè Ba Màu',
    description: 'Chè ba màu truyền thống với đậu xanh, đậu đỏ và thạch',
    price: 25000,
    category: 'Tráng Miệng',
    images: [
      'https://images.unsplash.com/photo-1563805042-7684c019e1cb?w=500'
    ],
    ingredients: ['Đậu xanh', 'Đậu đỏ', 'Thạch', 'Nước cốt dừa', 'Đá bào'],
    isAvailable: true,
    isFeatured: false,
    preparationTime: 5,
    nutrition: {
      calories: 180,
      protein: 5,
      carbs: 35,
      fat: 3
    }
  },
  {
    name: 'Nước Dừa Tươi',
    description: 'Nước dừa tươi mát lạnh, bổ sung điện giải tự nhiên',
    price: 20000,
    category: 'Đồ Uống',
    images: [
      'https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=500'
    ],
    ingredients: ['Nước dừa tươi', 'Đá'],
    isAvailable: true,
    isFeatured: false,
    preparationTime: 2,
    nutrition: {
      calories: 45,
      protein: 2,
      carbs: 9,
      fat: 0
    }
  },
  {
    name: 'Cà Phê Sữa Đá',
    description: 'Cà phê sữa đá đậm đà theo phong cách Việt Nam',
    price: 18000,
    category: 'Đồ Uống',
    images: [
      'https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=500'
    ],
    ingredients: ['Cà phê phin', 'Sữa đặc', 'Đá'],
    isAvailable: true,
    isFeatured: true,
    preparationTime: 8,
    nutrition: {
      calories: 120,
      protein: 3,
      carbs: 15,
      fat: 5
    }
  }
];

// Seed function
const seedProducts = async () => {
  try {
    // Find or create admin user
    let adminUser = await User.findOne({ email: '<EMAIL>' });
    if (!adminUser) {
      adminUser = await User.create({
        name: 'Admin',
        email: '<EMAIL>',
        password: 'admin123',
        role: 'admin'
      });
      console.log('Created admin user');
    }

    // Clear existing products
    await Product.deleteMany({});
    console.log('Cleared existing products');

    // Get sample products with admin user ID
    const sampleProducts = getSampleProducts(adminUser._id);

    // Insert sample products
    const products = await Product.insertMany(sampleProducts);
    console.log(`Inserted ${products.length} sample products`);

    console.log('Sample products:');
    products.forEach(product => {
      console.log(`- ${product.name} (${product.category}) - ${product.price}đ`);
    });

  } catch (error) {
    console.error('Error seeding products:', error);
  }
};

// Run the seed
const runSeed = async () => {
  await connectDB();
  await seedProducts();
  process.exit(0);
};

runSeed();
