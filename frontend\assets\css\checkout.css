/* Checkout Page Styles */

.checkout-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 40px;
    align-items: start;
}

.checkout-form {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.checkout-form h2 {
    color: #333;
    margin-bottom: 25px;
    font-size: 24px;
    font-weight: 600;
}

.form-section {
    margin-bottom: 30px;
    padding-bottom: 25px;
    border-bottom: 1px solid #e9ecef;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-section h3 i {
    color: #007bff;
    font-size: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 600;
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-group.error input,
.form-group.error select {
    border-color: #dc3545;
    background: #fff5f5;
}

.form-error {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    display: none;
}

.form-group.error .form-error {
    display: block;
}

/* Payment Methods */
.payment-methods {
    display: grid;
    gap: 15px;
}

.payment-method {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 15px;
}

.payment-method:hover {
    border-color: #007bff;
    background: rgba(0, 123, 255, 0.05);
}

.payment-method.selected {
    border-color: #007bff;
    background: rgba(0, 123, 255, 0.1);
}

.payment-method input[type="radio"] {
    width: auto;
    margin: 0;
}

.payment-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: white;
}

.payment-cod .payment-icon {
    background: #28a745;
}

.payment-bank .payment-icon {
    background: #007bff;
}

.payment-ewallet .payment-icon {
    background: #17a2b8;
}

.payment-card .payment-icon {
    background: #6f42c1;
}

.payment-info h4 {
    margin-bottom: 5px;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.payment-info p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

/* Order Summary */
.order-summary {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 20px;
}

.order-summary h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: 600;
}

.order-items {
    margin-bottom: 20px;
}

.order-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #f1f3f4;
}

.order-item:last-child {
    border-bottom: none;
}

.item-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    object-fit: cover;
}

.item-info {
    flex: 1;
}

.item-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.item-details {
    color: #666;
    font-size: 12px;
    margin-bottom: 5px;
}

.item-quantity {
    color: #666;
    font-size: 14px;
}

.item-price {
    font-weight: 600;
    color: #333;
    font-size: 16px;
}

/* Order Totals */
.order-totals {
    padding: 20px 0;
    border-top: 2px solid #e9ecef;
}

.total-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 14px;
}

.total-row.subtotal {
    color: #666;
}

.total-row.delivery {
    color: #666;
}

.total-row.discount {
    color: #28a745;
}

.total-row.final {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
    margin-top: 15px;
}

/* Promo Code */
.promo-section {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.promo-input {
    display: flex;
    gap: 10px;
}

.promo-input input {
    flex: 1;
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
}

.promo-btn {
    padding: 10px 20px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.promo-btn:hover {
    background: #0056b3;
}

.promo-applied {
    margin-top: 10px;
    padding: 10px;
    background: #d4edda;
    color: #155724;
    border-radius: 6px;
    font-size: 14px;
    display: none;
}

.promo-applied.active {
    display: block;
}

/* Place Order Button */
.place-order-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.place-order-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(40, 167, 69, 0.3);
}

.place-order-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.place-order-btn.loading {
    position: relative;
    color: transparent;
}

.place-order-btn.loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid transparent;
    border-top-color: white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Security Info */
.security-info {
    margin-top: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: center;
}

.security-info i {
    color: #28a745;
    margin-right: 8px;
}

.security-info p {
    color: #666;
    font-size: 12px;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .checkout-container {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 15px;
    }
    
    .checkout-form,
    .order-summary {
        padding: 20px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .order-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .item-image {
        width: 50px;
        height: 50px;
    }
    
    .payment-method {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .checkout-form h2 {
        font-size: 20px;
    }
    
    .form-section h3 {
        font-size: 16px;
    }
    
    .order-summary h3 {
        font-size: 18px;
    }
    
    .promo-input {
        flex-direction: column;
    }
    
    .promo-btn {
        width: 100%;
    }
}
