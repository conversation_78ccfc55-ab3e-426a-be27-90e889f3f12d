// Admin Banner Management Module
const AdminBanner = {
    banners: [],

    init() {
        this.bindEvents();
    },

    bindEvents() {
        // Add banner button
        const addBtn = document.getElementById('add-banner-btn');
        if (addBtn) {
            addBtn.addEventListener('click', () => {
                this.showBannerModal();
            });
        }
    },

    async loadBanners() {
        try {
            Loading.show();

            // Mock data for now
            const mockBanners = [
                {
                    _id: '1',
                    title: '<PERSON>huyến mãi mùa hè',
                    description: 'Gi<PERSON>m giá 20% cho tất cả món ăn',
                    imageUrl: '/images/banner1.jpg',
                    linkUrl: '/products?category=all',
                    isActive: true,
                    displayOrder: 1,
                    startDate: new Date('2024-06-01'),
                    endDate: new Date('2024-08-31'),
                    createdAt: new Date('2024-05-15')
                },
                {
                    _id: '2',
                    title: '<PERSON><PERSON> mới ra mắt',
                    description: 'Thử ngay các món ăn mới tại Na Food',
                    imageUrl: '/images/banner2.jpg',
                    linkUrl: '/products?featured=true',
                    isActive: true,
                    displayOrder: 2,
                    startDate: new Date('2024-01-01'),
                    endDate: new Date('2024-12-31'),
                    createdAt: new Date('2024-01-01')
                }
            ];

            this.banners = mockBanners;
            this.renderBannersTable();

        } catch (error) {
            console.error('Load banners error:', error);
            Toast.error('Không thể tải danh sách banner');
        } finally {
            Loading.hide();
        }
    },

    renderBannersTable() {
        const container = document.getElementById('banners-table');
        if (!container) return;

        if (this.banners.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-image"></i>
                    <h3>Chưa có banner nào</h3>
                    <p>Thêm banner để quảng bá sản phẩm</p>
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Hình ảnh</th>
                        <th>Tiêu đề</th>
                        <th>Mô tả</th>
                        <th>Thứ tự</th>
                        <th>Trạng thái</th>
                        <th>Thời gian hiển thị</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    ${this.banners.map(banner => `
                        <tr>
                            <td>
                                <img src="${banner.imageUrl}" alt="${banner.title}" class="banner-thumb">
                            </td>
                            <td>
                                <div class="banner-info">
                                    <strong>${banner.title}</strong>
                                    ${banner.linkUrl ? `<small>Link: ${banner.linkUrl}</small>` : ''}
                                </div>
                            </td>
                            <td>
                                <span class="banner-description">${Utils.truncate(banner.description, 50)}</span>
                            </td>
                            <td>
                                <div class="order-controls">
                                    <button class="btn-order" onclick="AdminBanner.moveUp('${banner._id}')" title="Lên">
                                        <i class="fas fa-chevron-up"></i>
                                    </button>
                                    <span class="order-number">${banner.displayOrder}</span>
                                    <button class="btn-order" onclick="AdminBanner.moveDown('${banner._id}')" title="Xuống">
                                        <i class="fas fa-chevron-down"></i>
                                    </button>
                                </div>
                            </td>
                            <td>
                                <span class="status-badge ${banner.isActive ? 'status-active' : 'status-inactive'}">
                                    ${banner.isActive ? 'Hoạt động' : 'Tạm dừng'}
                                </span>
                            </td>
                            <td>
                                <div class="date-range">
                                    <small>Từ: ${Utils.formatDate(banner.startDate, { year: 'numeric', month: '2-digit', day: '2-digit' })}</small><br>
                                    <small>Đến: ${Utils.formatDate(banner.endDate, { year: 'numeric', month: '2-digit', day: '2-digit' })}</small>
                                </div>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-action btn-edit" onclick="AdminBanner.editBanner('${banner._id}')" title="Sửa">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-action btn-toggle" onclick="AdminBanner.toggleBanner('${banner._id}')" title="${banner.isActive ? 'Tạm dừng' : 'Kích hoạt'}">
                                        <i class="fas fa-${banner.isActive ? 'pause' : 'play'}"></i>
                                    </button>
                                    <button class="btn-action btn-delete" onclick="AdminBanner.deleteBanner('${banner._id}')" title="Xóa">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    },

    showBannerModal(banner = null) {
        const isEdit = !!banner;
        const modalContent = `
            <div class="banner-modal">
                <div class="modal-header">
                    <h2>${isEdit ? 'Sửa banner' : 'Thêm banner mới'}</h2>
                    <button class="modal-close" onclick="Modal.hide()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="banner-form" class="banner-form">
                        <div class="form-group">
                            <label>Tiêu đề *</label>
                            <input type="text" name="title" value="${banner?.title || ''}" required>
                        </div>
                        
                        <div class="form-group">
                            <label>Mô tả</label>
                            <textarea name="description" rows="3">${banner?.description || ''}</textarea>
                        </div>
                        
                        <div class="form-group">
                            <label>URL hình ảnh *</label>
                            <input type="url" name="imageUrl" value="${banner?.imageUrl || ''}" required>
                            <small>Nhập URL hình ảnh banner</small>
                        </div>
                        
                        <div class="form-group">
                            <label>Link đích</label>
                            <input type="text" name="linkUrl" value="${banner?.linkUrl || ''}" 
                                   placeholder="/products?category=appetizer">
                            <small>URL trang đích khi click banner</small>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label>Ngày bắt đầu *</label>
                                <input type="date" name="startDate" 
                                       value="${banner?.startDate ? banner.startDate.toISOString().split('T')[0] : ''}" required>
                            </div>
                            <div class="form-group">
                                <label>Ngày kết thúc *</label>
                                <input type="date" name="endDate" 
                                       value="${banner?.endDate ? banner.endDate.toISOString().split('T')[0] : ''}" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label>Thứ tự hiển thị</label>
                                <input type="number" name="displayOrder" value="${banner?.displayOrder || 1}" min="1">
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="isActive" ${banner?.isActive !== false ? 'checked' : ''}>
                                    Kích hoạt banner
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                ${isEdit ? 'Cập nhật' : 'Thêm banner'}
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="Modal.hide()">Hủy</button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        Modal.show(modalContent);

        // Bind form submit
        document.getElementById('banner-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveBanner(e.target, isEdit ? banner._id : null);
        });
    },

    async saveBanner(form, bannerId = null) {
        try {
            Loading.show();

            const formData = new FormData(form);
            const bannerData = {
                title: formData.get('title'),
                description: formData.get('description'),
                imageUrl: formData.get('imageUrl'),
                linkUrl: formData.get('linkUrl'),
                startDate: new Date(formData.get('startDate')),
                endDate: new Date(formData.get('endDate')),
                displayOrder: parseInt(formData.get('displayOrder')),
                isActive: formData.has('isActive')
            };

            if (bannerId) {
                // Mock update
                const bannerIndex = this.banners.findIndex(b => b._id === bannerId);
                if (bannerIndex !== -1) {
                    this.banners[bannerIndex] = { ...this.banners[bannerIndex], ...bannerData };
                }
                Toast.success('Cập nhật banner thành công!');
            } else {
                // Mock create
                const newBanner = {
                    _id: Date.now().toString(),
                    ...bannerData,
                    createdAt: new Date()
                };
                this.banners.push(newBanner);
                Toast.success('Thêm banner thành công!');
            }

            Modal.hide();
            this.renderBannersTable();

        } catch (error) {
            console.error('Save banner error:', error);
            Toast.error('Có lỗi xảy ra: ' + error.message);
        } finally {
            Loading.hide();
        }
    },

    async editBanner(bannerId) {
        const banner = this.banners.find(b => b._id === bannerId);
        if (banner) {
            this.showBannerModal(banner);
        }
    },

    async toggleBanner(bannerId) {
        try {
            const banner = this.banners.find(b => b._id === bannerId);
            if (banner) {
                banner.isActive = !banner.isActive;
                this.renderBannersTable();
                Toast.success(`${banner.isActive ? 'Kích hoạt' : 'Tạm dừng'} banner thành công!`);
            }

        } catch (error) {
            console.error('Toggle banner error:', error);
            Toast.error('Có lỗi xảy ra khi cập nhật trạng thái');
        }
    },

    async deleteBanner(bannerId) {
        if (!confirm('Bạn có chắc chắn muốn xóa banner này?')) {
            return;
        }

        try {
            this.banners = this.banners.filter(b => b._id !== bannerId);
            this.renderBannersTable();
            Toast.success('Xóa banner thành công!');

        } catch (error) {
            console.error('Delete banner error:', error);
            Toast.error('Có lỗi xảy ra khi xóa banner');
        }
    },

    async moveUp(bannerId) {
        const banner = this.banners.find(b => b._id === bannerId);
        if (banner && banner.displayOrder > 1) {
            // Find banner with order - 1
            const prevBanner = this.banners.find(b => b.displayOrder === banner.displayOrder - 1);
            if (prevBanner) {
                // Swap orders
                prevBanner.displayOrder++;
                banner.displayOrder--;
                this.renderBannersTable();
                Toast.success('Đã di chuyển banner lên trên!');
            }
        }
    },

    async moveDown(bannerId) {
        const banner = this.banners.find(b => b._id === bannerId);
        const maxOrder = Math.max(...this.banners.map(b => b.displayOrder));
        
        if (banner && banner.displayOrder < maxOrder) {
            // Find banner with order + 1
            const nextBanner = this.banners.find(b => b.displayOrder === banner.displayOrder + 1);
            if (nextBanner) {
                // Swap orders
                nextBanner.displayOrder--;
                banner.displayOrder++;
                this.renderBannersTable();
                Toast.success('Đã di chuyển banner xuống dưới!');
            }
        }
    }
};

// Export for global use
window.AdminBanner = AdminBanner;
