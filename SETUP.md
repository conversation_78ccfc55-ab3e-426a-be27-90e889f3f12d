# 🚀 Na Food - Hướng Dẫn Cài Đặt và Chạy Server

## 📋 <PERSON>ụ<PERSON>
- [<PERSON><PERSON>u <PERSON>](#yêu-cầu-hệ-thống)
- [Cài Đặt Nhanh với Docker](#cài-đặt-nhanh-với-docker)
- [Cài Đặt Development](#cài-đặt-development)
- [Cấu Hình Database](#cấu-hình-database)
- [Chạy Ứng Dụng](#chạy-ứng-dụng)
- [Tài Khoản Mặc Định](#tài-khoản-mặc-định)
- [API Endpoints](#api-endpoints)
- [Troubleshooting](#troubleshooting)

## 🔧 Yêu Cầu Hệ Thống

### Minimum Requirements:
- **Node.js**: 18.0+ 
- **MongoDB**: 5.0+ hoặc MongoDB Atlas
- **RAM**: 2GB+
- **Storage**: 1GB+

### Recommended:
- **Docker & Docker Compose** (cho cài đặt nhanh)
- **Git** (để clone repository)
- **VS Code** (IDE khuyến nghị)

## 🐳 Cài Đặt Nhanh với Docker (Khuyến Nghị)

### Bước 1: Clone Repository
```bash
git clone <repository-url>
cd na-food
```

### Bước 2: Cấu Hình Environment
```bash
# Copy file environment mẫu
cp backend/.env.example backend/.env

# Chỉnh sửa file .env nếu cần
# Mặc định đã được cấu hình sẵn cho Docker
```

### Bước 3: Khởi Động với Docker Compose
```bash
# Khởi động tất cả services
docker-compose up -d

# Xem logs
docker-compose logs -f

# Kiểm tra trạng thái
docker-compose ps
```

### Bước 4: Tạo Admin User
```bash
# Chạy script tạo admin
docker-compose exec backend node createAdmin.js
```

### Bước 5: Truy Cập Ứng Dụng
- **Frontend**: http://localhost:3000
- **Admin Panel**: http://localhost:3000/admin.html
- **API**: http://localhost:5000
- **MongoDB**: localhost:27017

## 💻 Cài Đặt Development (Local)

### Bước 1: Cài Đặt Dependencies

#### Backend:
```bash
cd backend
npm install
```

#### Frontend:
```bash
# Frontend chỉ cần static files, không cần install
# Hoặc sử dụng live server
npm install -g live-server
```

### Bước 2: Cấu Hình Database

#### Option A: MongoDB Local
```bash
# Cài đặt MongoDB Community Edition
# Windows: https://docs.mongodb.com/manual/tutorial/install-mongodb-on-windows/
# macOS: brew install mongodb-community
# Ubuntu: sudo apt install mongodb

# Khởi động MongoDB
mongod --dbpath /path/to/data/directory
```

#### Option B: MongoDB Atlas (Khuyến nghị)
1. Tạo account tại [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Tạo cluster miễn phí
3. Lấy connection string
4. Cập nhật `MONGO_URI` trong file `.env`

### Bước 3: Cấu Hình Environment Variables
```bash
cd backend
cp .env.example .env
```

Chỉnh sửa file `.env`:
```env
# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
MONGO_URI=mongodb://localhost:27017/nafood
# Hoặc MongoDB Atlas:
# MONGO_URI=mongodb+srv://username:<EMAIL>/nafood

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this
JWT_EXPIRE=7d

# File Upload Configuration
MAX_FILE_SIZE=5000000
FILE_UPLOAD_PATH=./uploads

# Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100
```

### Bước 4: Khởi Động Services

#### Terminal 1 - Backend:
```bash
cd backend
npm run dev
# Hoặc: npm start
```

#### Terminal 2 - Frontend:
```bash
cd frontend
# Option 1: Python
python -m http.server 3000

# Option 2: Node.js serve
npx serve -s . -l 3000

# Option 3: Live Server (VS Code extension)
# Hoặc: live-server --port=3000
```

### Bước 5: Tạo Admin User
```bash
cd backend
node createAdmin.js
```

## 🗄️ Cấu Hình Database

### MongoDB Atlas Setup:
1. **Tạo Account**: Đăng ký tại mongodb.com/atlas
2. **Tạo Cluster**: Chọn free tier (M0)
3. **Whitelist IP**: Thêm IP address hoặc 0.0.0.0/0 cho development
4. **Tạo User**: Tạo database user với quyền read/write
5. **Connection String**: Copy connection string và update vào .env

### Local MongoDB Setup:
```bash
# Khởi động MongoDB
mongod

# Kết nối với MongoDB shell
mongo

# Tạo database và user
use nafood
db.createUser({
  user: "nafood_user",
  pwd: "password123",
  roles: ["readWrite"]
})
```

## 🚀 Chạy Ứng Dụng

### Development Mode:
```bash
# Backend (Terminal 1)
cd backend
npm run dev

# Frontend (Terminal 2)  
cd frontend
python -m http.server 3000
```

### Production Mode với Docker:
```bash
# Build và chạy
docker-compose -f docker-compose.prod.yml up -d

# Hoặc với file mặc định
docker-compose up -d
```

### Kiểm Tra Health:
```bash
# API Health Check
curl http://localhost:5000/api/health

# Hoặc mở browser
http://localhost:5000/api/health
```

## 🔑 Tài Khoản Mặc Định

### Admin Account:
- **Email**: <EMAIL>
- **Password**: admin123
- **Role**: Admin (full access)

### Test User Account:
- **Email**: <EMAIL>  
- **Password**: password123
- **Role**: User (customer)

## 📡 API Endpoints

### Authentication:
```
POST /api/auth/register    # Đăng ký
POST /api/auth/login       # Đăng nhập
GET  /api/auth/me          # Thông tin user
PUT  /api/auth/profile     # Cập nhật profile
```

### Products:
```
GET    /api/products           # Danh sách sản phẩm
GET    /api/products/:id       # Chi tiết sản phẩm
POST   /api/products           # Thêm sản phẩm (Admin)
PUT    /api/products/:id       # Sửa sản phẩm (Admin)
DELETE /api/products/:id       # Xóa sản phẩm (Admin)
```

### Orders:
```
GET  /api/orders              # Danh sách đơn hàng (Admin/Staff)
POST /api/orders              # Tạo đơn hàng
GET  /api/orders/me           # Đơn hàng của user
GET  /api/orders/:id          # Chi tiết đơn hàng
PUT  /api/orders/:id/status   # Cập nhật trạng thái (Admin/Staff)
```

### Export:
```
GET /api/orders/export/pdf    # Xuất PDF
GET /api/orders/export/csv    # Xuất CSV
```

## 🔧 Troubleshooting

### Lỗi Thường Gặp:

#### 1. MongoDB Connection Error:
```bash
# Kiểm tra MongoDB đang chạy
ps aux | grep mongod

# Khởi động MongoDB
sudo systemctl start mongod

# Kiểm tra connection string trong .env
```

#### 2. Port Already in Use:
```bash
# Kiểm tra port đang sử dụng
lsof -i :5000
lsof -i :3000

# Kill process
kill -9 <PID>

# Hoặc thay đổi port trong .env
```

#### 3. Permission Denied:
```bash
# Cấp quyền cho thư mục uploads
chmod 755 backend/uploads

# Hoặc tạo thư mục nếu chưa có
mkdir -p backend/uploads
```

#### 4. Docker Issues:
```bash
# Xem logs chi tiết
docker-compose logs backend
docker-compose logs mongo

# Restart services
docker-compose restart

# Rebuild containers
docker-compose down
docker-compose up --build
```

#### 5. Frontend Not Loading:
```bash
# Kiểm tra file index.html tồn tại
ls frontend/index.html

# Kiểm tra server đang chạy
curl http://localhost:3000

# Thử browser khác hoặc incognito mode
```

### Debug Commands:

#### Kiểm tra Database:
```bash
# MongoDB shell
mongo nafood

# Xem collections
show collections

# Xem users
db.users.find()

# Xem products  
db.products.find()
```

#### Kiểm tra API:
```bash
# Test health endpoint
curl http://localhost:5000/api/health

# Test login
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'

# Test products
curl http://localhost:5000/api/products
```

## 📞 Hỗ Trợ

### Logs Location:
- **Backend**: Console output hoặc `backend/logs/`
- **MongoDB**: `/var/log/mongodb/mongod.log`
- **Docker**: `docker-compose logs`

### Useful Commands:
```bash
# Xem tất cả containers
docker ps -a

# Xem logs realtime
docker-compose logs -f backend

# Restart specific service
docker-compose restart backend

# Clean up
docker-compose down -v
docker system prune
```

### Environment Variables Reference:
```env
PORT=5000                                    # Server port
NODE_ENV=development                         # Environment
MONGO_URI=mongodb://localhost:27017/nafood   # Database URL
JWT_SECRET=your-secret-key                   # JWT secret
JWT_EXPIRE=7d                               # Token expiry
MAX_FILE_SIZE=5000000                       # Upload limit
FILE_UPLOAD_PATH=./uploads                  # Upload directory
RATE_LIMIT_WINDOW=15                        # Rate limit window (minutes)
RATE_LIMIT_MAX_REQUESTS=100                 # Max requests per window
```

---

## 🎉 Chúc Mừng!

Nếu mọi thứ chạy thành công, bạn sẽ thấy:
- ✅ Backend API tại http://localhost:5000
- ✅ Frontend tại http://localhost:3000  
- ✅ Admin Panel tại http://localhost:3000/admin.html
- ✅ Database connection thành công

**Happy Coding! 🚀**
