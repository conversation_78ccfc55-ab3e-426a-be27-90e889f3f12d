/* Product Detail Page Styles */

.product-detail-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.product-detail-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-bottom: 40px;
}

/* Product Images */
.product-images {
    position: relative;
}

.main-image-container {
    position: relative;
    margin-bottom: 20px;
    border-radius: 12px;
    overflow: hidden;
    background: #f8f9fa;
}

.main-product-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    cursor: zoom-in;
    transition: transform 0.3s ease;
}

.main-product-image:hover {
    transform: scale(1.05);
}

.image-thumbnails {
    display: flex;
    gap: 10px;
    overflow-x: auto;
    padding: 10px 0;
}

.thumbnail {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.thumbnail:hover,
.thumbnail.active {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

/* Product Info */
.product-info {
    padding: 20px 0;
}

.product-name {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 15px;
    line-height: 1.2;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.stars {
    display: flex;
    gap: 2px;
}

.stars i {
    color: #ffc107;
    font-size: 1.2rem;
}

.rating-text {
    color: var(--text-muted);
    font-size: 0.95rem;
}

.product-price {
    margin-bottom: 25px;
}

.current-price {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.sale-price {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-right: 15px;
}

.original-price {
    font-size: 1.5rem;
    color: var(--text-muted);
    text-decoration: line-through;
    margin-right: 10px;
}

.discount-badge {
    background: var(--danger-color);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
}

.product-description {
    color: var(--text-muted);
    line-height: 1.6;
    margin-bottom: 30px;
    font-size: 1.1rem;
}

/* Quantity Controls */
.quantity-section {
    margin-bottom: 30px;
}

.quantity-label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: var(--text-dark);
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.quantity-input-group {
    display: flex;
    align-items: center;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.quantity-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.quantity-btn:hover {
    background: var(--primary-dark);
}

.quantity-input {
    border: none;
    width: 60px;
    height: 40px;
    text-align: center;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Action Buttons */
.product-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
}

.action-btn {
    flex: 1;
    padding: 15px 25px;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-add-cart {
    background: var(--primary-color);
    color: white;
}

.btn-add-cart:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

.btn-buy-now {
    background: var(--success-color);
    color: white;
}

.btn-buy-now:hover {
    background: var(--success-dark);
    transform: translateY(-2px);
}

/* Product Details Tabs */
.product-tabs {
    margin-top: 40px;
    border-top: 1px solid #e9ecef;
    padding-top: 40px;
}

.tab-nav {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
    border-bottom: 1px solid #e9ecef;
}

.tab-btn {
    background: none;
    border: none;
    padding: 15px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-muted);
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-content {
    display: none;
    animation: fadeIn 0.3s ease;
}

.tab-content.active {
    display: block;
}

/* Reviews Section */
.reviews-section {
    margin-top: 40px;
}

.review-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.review-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.reviewer-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.reviewer-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.2rem;
}

.reviewer-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.review-rating {
    margin: 5px 0;
}

.review-date {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.review-content {
    line-height: 1.6;
    color: var(--text-dark);
}

/* Responsive Design */
@media (max-width: 768px) {
    .product-detail-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .product-name {
        font-size: 2rem;
    }
    
    .product-actions {
        flex-direction: column;
    }
    
    .tab-nav {
        flex-wrap: wrap;
        gap: 15px;
    }
    
    .quantity-controls {
        justify-content: center;
    }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
