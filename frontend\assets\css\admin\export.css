/* Admin Export Styles */

.export-container {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 30px;
}

.export-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.export-header h3 {
    color: #333;
    font-size: 20px;
    font-weight: 600;
    margin: 0;
}

.export-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.export-option {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.export-option:hover {
    border-color: #007bff;
    background: rgba(0, 123, 255, 0.05);
    transform: translateY(-2px);
}

.export-option.selected {
    border-color: #007bff;
    background: rgba(0, 123, 255, 0.1);
}

.export-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 15px;
    background: #007bff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.export-option.pdf .export-icon {
    background: #dc3545;
}

.export-option.csv .export-icon {
    background: #28a745;
}

.export-option.excel .export-icon {
    background: #17a2b8;
}

.export-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.export-description {
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

/* Export Filters */
.export-filters {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 25px;
}

.export-filters h4 {
    color: #333;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-size: 12px;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
}

.filter-group input,
.filter-group select {
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Export Progress */
.export-progress {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
    display: none;
}

.export-progress.active {
    display: block;
}

.progress-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.progress-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #007bff;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

.progress-info h4 {
    color: #333;
    margin-bottom: 5px;
    font-size: 16px;
}

.progress-info p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    text-align: center;
    color: #666;
    font-size: 12px;
    font-weight: 500;
}

/* Export Actions */
.export-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
}

.export-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    min-width: 150px;
    justify-content: center;
}

.export-btn.primary {
    background: #007bff;
    color: white;
}

.export-btn.primary:hover {
    background: #0056b3;
    transform: translateY(-2px);
}

.export-btn.secondary {
    background: #6c757d;
    color: white;
}

.export-btn.secondary:hover {
    background: #545b62;
    transform: translateY(-2px);
}

.export-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Export History */
.export-history {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.export-history h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
}

.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #e9ecef;
}

.history-item:last-child {
    border-bottom: none;
}

.history-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.history-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: white;
}

.history-icon.pdf {
    background: #dc3545;
}

.history-icon.csv {
    background: #28a745;
}

.history-details h4 {
    color: #333;
    margin-bottom: 4px;
    font-size: 14px;
    font-weight: 600;
}

.history-details p {
    color: #666;
    font-size: 12px;
    margin: 0;
}

.history-actions {
    display: flex;
    gap: 10px;
}

.history-btn {
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.history-btn.download {
    background: #007bff;
    color: white;
}

.history-btn.download:hover {
    background: #0056b3;
}

.history-btn.delete {
    background: #dc3545;
    color: white;
}

.history-btn.delete:hover {
    background: #c82333;
}

/* Responsive Design */
@media (max-width: 768px) {
    .export-options {
        grid-template-columns: 1fr;
    }
    
    .filter-grid {
        grid-template-columns: 1fr;
    }
    
    .export-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .export-btn {
        width: 100%;
        max-width: 300px;
    }
    
    .history-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .history-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

@media (max-width: 480px) {
    .export-container,
    .export-history {
        padding: 20px;
    }
    
    .export-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .progress-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}
