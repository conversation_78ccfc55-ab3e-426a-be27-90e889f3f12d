<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Na Food - Đặt món ăn trực tuyến, giao hàng tận nơi">
    <meta name="keywords" content="đặt món, giao hàng, thức ăn, na food">
    <title>Na Food - Đặt Món Ăn Trực Tuyến</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/index/header.css">
    <link rel="stylesheet" href="assets/css/index/products.css">
    <link rel="stylesheet" href="assets/css/index/detail.css">
    <link rel="stylesheet" href="assets/css/index/cart.css">
    <link rel="stylesheet" href="assets/css/index/checkout.css">
    <link rel="stylesheet" href="assets/css/index/orders.css">
    <link rel="stylesheet" href="assets/css/index/auth.css">
    <link rel="stylesheet" href="assets/css/index/review.css">
    <link rel="stylesheet" href="assets/css/index/payment.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Loading Spinner -->
    <div id="loading-spinner" class="loading-spinner">
        <div class="spinner"></div>
        <p>Đang tải...</p>
    </div>

    <!-- Header -->
    <header id="header" class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <img src="/images/logo.png" alt="Na Food" class="logo-img">
                    <span class="logo-text">Na Food</span>
                </div>
                
                <div class="nav-menu">
                    <a href="#home" class="nav-link active" data-page="home">
                        <i class="fas fa-home"></i> Trang chủ
                    </a>
                    <a href="#products" class="nav-link" data-page="products">
                        <i class="fas fa-utensils"></i> Thực đơn
                    </a>
                    <a href="#about" class="nav-link" data-page="about">
                        <i class="fas fa-info-circle"></i> Giới thiệu
                    </a>
                    <a href="#contact" class="nav-link" data-page="contact">
                        <i class="fas fa-phone"></i> Liên hệ
                    </a>
                </div>

                <div class="nav-actions">
                    <div class="search-box">
                        <input type="text" id="search-input" placeholder="Tìm món ăn...">
                        <button id="search-btn"><i class="fas fa-search"></i></button>
                    </div>
                    
                    <div class="cart-icon" id="cart-icon">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="cart-count" id="cart-count">0</span>
                    </div>
                    
                    <div class="user-menu" id="user-menu">
                        <div class="user-info" id="user-info">
                            <i class="fas fa-user"></i>
                            <span id="user-name">Đăng nhập</span>
                        </div>
                        <div class="user-dropdown" id="user-dropdown">
                            <a href="#profile" id="profile-link"><i class="fas fa-user-circle"></i> Hồ sơ</a>
                            <a href="#orders" id="orders-link"><i class="fas fa-list-alt"></i> Đơn hàng</a>
                            <a href="#logout" id="logout-link"><i class="fas fa-sign-out-alt"></i> Đăng xuất</a>
                        </div>
                    </div>
                </div>

                <div class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main id="main-content" class="main-content">
        <!-- Home Page -->
        <section id="home-page" class="page active">
            <div class="hero-section">
                <div id="banner-slider" class="banner-slider">
                    <!-- Banners will be loaded here -->
                </div>
            </div>
            
            <div class="featured-section">
                <div class="container">
                    <h2>Món ăn nổi bật</h2>
                    <div id="featured-products" class="product-grid">
                        <!-- Featured products will be loaded here -->
                    </div>
                </div>
            </div>
            
            <div class="categories-section">
                <div class="container">
                    <h2>Danh mục món ăn</h2>
                    <div id="categories-grid" class="categories-grid">
                        <!-- Categories will be loaded here -->
                    </div>
                </div>
            </div>
        </section>

        <!-- Products Page -->
        <section id="products-page" class="page">
            <div class="container">
                <div class="products-header">
                    <h1>Thực đơn</h1>
                    <div class="products-filters">
                        <select id="category-filter">
                            <option value="">Tất cả danh mục</option>
                        </select>
                        <select id="sort-filter">
                            <option value="-createdAt">Mới nhất</option>
                            <option value="price">Giá thấp đến cao</option>
                            <option value="-price">Giá cao đến thấp</option>
                            <option value="-rating">Đánh giá cao nhất</option>
                            <option value="-soldCount">Bán chạy nhất</option>
                        </select>
                        <div class="price-range">
                            <input type="number" id="min-price" placeholder="Giá từ">
                            <input type="number" id="max-price" placeholder="Giá đến">
                            <button id="apply-price-filter">Áp dụng</button>
                        </div>
                    </div>
                </div>
                
                <div id="products-grid" class="product-grid">
                    <!-- Products will be loaded here -->
                </div>
                
                <div id="products-pagination" class="pagination">
                    <!-- Pagination will be loaded here -->
                </div>
            </div>
        </section>

        <!-- Product Detail Page -->
        <section id="product-detail-page" class="page">
            <div class="container">
                <div id="product-detail" class="product-detail">
                    <!-- Product detail will be loaded here -->
                </div>
            </div>
        </section>

        <!-- Cart Page -->
        <section id="cart-page" class="page">
            <div class="container">
                <h1>Giỏ hàng</h1>
                <div id="cart-content" class="cart-content">
                    <!-- Cart items will be loaded here -->
                </div>
            </div>
        </section>

        <!-- Checkout Page -->
        <section id="checkout-page" class="page">
            <div class="container">
                <h1>Thanh toán</h1>
                <div id="checkout-content" class="checkout-content">
                    <!-- Checkout form will be loaded here -->
                </div>
            </div>
        </section>

        <!-- Orders Page -->
        <section id="orders-page" class="page">
            <div class="container">
                <h1>Đơn hàng của tôi</h1>
                <div id="orders-content" class="orders-content">
                    <!-- Orders will be loaded here -->
                </div>
            </div>
        </section>

        <!-- Auth Pages -->
        <section id="auth-page" class="page">
            <div class="container">
                <div id="auth-content" class="auth-content">
                    <!-- Auth forms will be loaded here -->
                </div>
            </div>
        </section>

        <!-- About Page -->
        <section id="about-page" class="page">
            <div class="container">
                <h1>Giới thiệu về Na Food</h1>
                <div class="about-content">
                    <p>Na Food là nền tảng đặt món ăn trực tuyến hàng đầu, mang đến cho bạn những trải nghiệm ẩm thực tuyệt vời nhất.</p>
                </div>
            </div>
        </section>

        <!-- Contact Page -->
        <section id="contact-page" class="page">
            <div class="container">
                <h1>Liên hệ</h1>
                <div class="contact-content">
                    <div class="contact-info">
                        <p><i class="fas fa-phone"></i> Hotline: 1900 1234</p>
                        <p><i class="fas fa-envelope"></i> Email: <EMAIL></p>
                        <p><i class="fas fa-map-marker-alt"></i> Địa chỉ: 123 Đường ABC, Quận XYZ, TP.HCM</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer id="footer" class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Na Food</h3>
                    <p>Nền tảng đặt món ăn trực tuyến hàng đầu Việt Nam</p>
                </div>
                <div class="footer-section">
                    <h3>Liên kết nhanh</h3>
                    <ul>
                        <li><a href="#home">Trang chủ</a></li>
                        <li><a href="#products">Thực đơn</a></li>
                        <li><a href="#about">Giới thiệu</a></li>
                        <li><a href="#contact">Liên hệ</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Hỗ trợ</h3>
                    <ul>
                        <li><a href="#help">Trợ giúp</a></li>
                        <li><a href="#terms">Điều khoản</a></li>
                        <li><a href="#privacy">Bảo mật</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Theo dõi chúng tôi</h3>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Na Food. Tất cả quyền được bảo lưu.</p>
            </div>
        </div>
    </footer>

    <!-- Modals -->
    <div id="modal-overlay" class="modal-overlay">
        <div id="modal-content" class="modal-content">
            <!-- Modal content will be loaded here -->
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container">
        <!-- Toast notifications will appear here -->
    </div>

    <!-- JavaScript Files -->
    <script src="assets/js/utils.js"></script>
    <script src="assets/js/index/header.js"></script>
    <script src="assets/js/index/products.js"></script>
    <script src="assets/js/index/detail.js"></script>
    <script src="assets/js/index/cart.js"></script>
    <script src="assets/js/index/checkout.js"></script>
    <script src="assets/js/index/orders.js"></script>
    <script src="assets/js/index/auth.js"></script>
    <script src="assets/js/index/review.js"></script>
    <script src="assets/js/index/payment.js"></script>
    
    <!-- Main App Script -->
    <script>
        // Initialize app when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize all modules
            if (typeof HeaderModule !== 'undefined') HeaderModule.init();
            if (typeof ProductsModule !== 'undefined') ProductsModule.init();
            if (typeof CartModule !== 'undefined') CartModule.init();
            if (typeof AuthModule !== 'undefined') AuthModule.init();
            
            // Hide loading spinner
            document.getElementById('loading-spinner').style.display = 'none';
        });
    </script>
</body>
</html>
