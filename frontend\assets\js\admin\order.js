// Admin Order Management Module
const AdminOrder = {
    orders: [],
    currentFilters: {
        search: '',
        status: '',
        paymentStatus: '',
        dateFrom: '',
        dateTo: '',
        page: 1,
        limit: 20
    },

    init() {
        this.bindEvents();
    },

    bindEvents() {
        // Search and filters
        const searchInput = document.getElementById('order-search');
        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce(() => {
                this.currentFilters.search = searchInput.value;
                this.currentFilters.page = 1;
                this.loadOrders();
            }, 500));
        }

        const statusFilter = document.getElementById('order-status-filter');
        if (statusFilter) {
            statusFilter.addEventListener('change', () => {
                this.currentFilters.status = statusFilter.value;
                this.currentFilters.page = 1;
                this.loadOrders();
            });
        }

        const dateFromInput = document.getElementById('order-date-from');
        const dateToInput = document.getElementById('order-date-to');
        
        if (dateFromInput) {
            dateFromInput.addEventListener('change', () => {
                this.currentFilters.dateFrom = dateFromInput.value;
                this.currentFilters.page = 1;
                this.loadOrders();
            });
        }

        if (dateToInput) {
            dateToInput.addEventListener('change', () => {
                this.currentFilters.dateTo = dateToInput.value;
                this.currentFilters.page = 1;
                this.loadOrders();
            });
        }

        // Export button
        const exportBtn = document.getElementById('export-orders-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportOrders();
            });
        }
    },

    async loadOrders() {
        try {
            Loading.show();

            const params = new URLSearchParams();
            Object.keys(this.currentFilters).forEach(key => {
                if (this.currentFilters[key]) {
                    params.append(key, this.currentFilters[key]);
                }
            });

            const response = await API.get('/orders', Object.fromEntries(params));
            this.orders = response.data;

            this.renderOrdersTable(response);
        } catch (error) {
            console.error('Load orders error:', error);
            Toast.error('Không thể tải danh sách đơn hàng');
        } finally {
            Loading.hide();
        }
    },

    renderOrdersTable(response) {
        const container = document.getElementById('orders-table');
        if (!container) return;

        if (this.orders.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-shopping-cart"></i>
                    <h3>Chưa có đơn hàng nào</h3>
                    <p>Đơn hàng sẽ hiển thị ở đây khi có khách đặt hàng</p>
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <div class="order-table">
                <div class="order-row header">
                    <div>Mã đơn</div>
                    <div>Khách hàng</div>
                    <div>Ngày đặt</div>
                    <div>Tổng tiền</div>
                    <div>Thanh toán</div>
                    <div>Trạng thái</div>
                    <div>Thao tác</div>
                </div>
                ${this.orders.map(order => `
                    <div class="order-row">
                        <div class="order-number">${order.orderNumber}</div>
                        <div class="customer-info">
                            <div class="customer-name">${order.deliveryInfo.name}</div>
                            <div class="customer-phone">${order.deliveryInfo.phone}</div>
                        </div>
                        <div class="order-date">${Utils.formatDate(order.createdAt)}</div>
                        <div class="order-total">${Utils.formatCurrency(order.total)}</div>
                        <div class="payment-info">
                            <div class="payment-method">${this.getPaymentMethodText(order.paymentMethod)}</div>
                            <span class="payment-status payment-${order.paymentStatus}">
                                ${this.getPaymentStatusText(order.paymentStatus)}
                            </span>
                        </div>
                        <div class="order-status">
                            <select class="status-select status-${order.status}" 
                                    onchange="AdminOrder.updateOrderStatus('${order._id}', this.value)">
                                ${this.getStatusOptions(order.status)}
                            </select>
                        </div>
                        <div class="order-actions">
                            <button class="action-btn btn-view" onclick="AdminOrder.viewOrder('${order._id}')" title="Xem chi tiết">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-btn btn-edit" onclick="AdminOrder.editOrder('${order._id}')" title="Sửa">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn btn-delete" onclick="AdminOrder.deleteOrder('${order._id}')" title="Xóa">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `).join('')}
            </div>
            
            ${this.renderPagination(response.pagination)}
        `;
    },

    renderPagination(pagination) {
        if (!pagination) return '';

        let paginationHTML = '<div class="pagination">';

        if (pagination.prev) {
            paginationHTML += `<button class="pagination-btn" onclick="AdminOrder.goToPage(${pagination.prev.page})">
                <i class="fas fa-chevron-left"></i> Trước
            </button>`;
        }

        paginationHTML += `<span class="pagination-info">Trang ${this.currentFilters.page}</span>`;

        if (pagination.next) {
            paginationHTML += `<button class="pagination-btn" onclick="AdminOrder.goToPage(${pagination.next.page})">
                Sau <i class="fas fa-chevron-right"></i>
            </button>`;
        }

        paginationHTML += '</div>';
        return paginationHTML;
    },

    goToPage(page) {
        this.currentFilters.page = page;
        this.loadOrders();
    },

    async updateOrderStatus(orderId, newStatus) {
        try {
            const response = await API.put(`/orders/${orderId}/status`, {
                status: newStatus
            });

            Toast.success('Cập nhật trạng thái đơn hàng thành công!');
            this.loadOrders();

        } catch (error) {
            console.error('Update order status error:', error);
            Toast.error('Có lỗi xảy ra khi cập nhật trạng thái');
            this.loadOrders(); // Reload to reset the select
        }
    },

    async viewOrder(orderId) {
        try {
            Loading.show();
            
            const response = await API.get(`/orders/${orderId}`);
            const order = response.data;

            this.showOrderDetailModal(order);

        } catch (error) {
            console.error('View order error:', error);
            Toast.error('Không thể tải chi tiết đơn hàng');
        } finally {
            Loading.hide();
        }
    },

    showOrderDetailModal(order) {
        const modalContent = `
            <div class="order-detail-modal">
                <div class="modal-header">
                    <h2>Chi tiết đơn hàng ${order.orderNumber}</h2>
                    <button class="modal-close" onclick="Modal.hide()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="order-detail-info">
                        <div class="info-section">
                            <h4>Thông tin đơn hàng</h4>
                            <div class="info-item">
                                <span class="info-label">Mã đơn:</span>
                                <span class="info-value">${order.orderNumber}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Ngày đặt:</span>
                                <span class="info-value">${Utils.formatDate(order.createdAt)}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Trạng thái:</span>
                                <span class="info-value">${this.getStatusText(order.status)}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Thanh toán:</span>
                                <span class="info-value">${this.getPaymentMethodText(order.paymentMethod)}</span>
                            </div>
                        </div>
                        
                        <div class="info-section">
                            <h4>Thông tin giao hàng</h4>
                            <div class="info-item">
                                <span class="info-label">Tên:</span>
                                <span class="info-value">${order.deliveryInfo.name}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Điện thoại:</span>
                                <span class="info-value">${order.deliveryInfo.phone}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Địa chỉ:</span>
                                <span class="info-value">${order.fullDeliveryAddress}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="order-items">
                        <h4>Món ăn đã đặt</h4>
                        <div class="item-row header">
                            <div>Hình</div>
                            <div>Món ăn</div>
                            <div>SL</div>
                            <div>Đơn giá</div>
                            <div>Thành tiền</div>
                        </div>
                        ${order.items.map(item => `
                            <div class="item-row">
                                <img src="${item.image || '/images/placeholder.jpg'}" alt="${item.name}" class="item-image">
                                <div class="item-info">
                                    <div class="item-name">${item.name}</div>
                                    ${item.specialInstructions ? `<div class="item-description">${item.specialInstructions}</div>` : ''}
                                </div>
                                <div class="item-quantity">${item.quantity}</div>
                                <div class="item-price">${Utils.formatCurrency(item.price)}</div>
                                <div class="item-total">${Utils.formatCurrency(item.price * item.quantity)}</div>
                            </div>
                        `).join('')}
                    </div>
                    
                    <div class="order-summary">
                        <div class="summary-row">
                            <span>Tạm tính:</span>
                            <span>${Utils.formatCurrency(order.subtotal)}</span>
                        </div>
                        <div class="summary-row">
                            <span>Phí giao hàng:</span>
                            <span>${Utils.formatCurrency(order.deliveryFee)}</span>
                        </div>
                        ${order.discount.amount > 0 ? `
                            <div class="summary-row">
                                <span>Giảm giá:</span>
                                <span>-${Utils.formatCurrency(order.discount.amount)}</span>
                            </div>
                        ` : ''}
                        <div class="summary-row total">
                            <span>Tổng cộng:</span>
                            <span>${Utils.formatCurrency(order.total)}</span>
                        </div>
                    </div>
                    
                    <div class="status-update-form">
                        <h4>Cập nhật trạng thái</h4>
                        <form id="status-update-form">
                            <div class="status-form-group">
                                <label>Trạng thái mới:</label>
                                <select name="status" required>
                                    ${this.getStatusOptions(order.status)}
                                </select>
                            </div>
                            <div class="status-form-group">
                                <label>Ghi chú:</label>
                                <textarea name="note" rows="3" placeholder="Ghi chú về việc cập nhật trạng thái..."></textarea>
                            </div>
                            <div class="status-form-actions">
                                <button type="submit" class="btn btn-primary">Cập nhật</button>
                                <button type="button" class="btn btn-secondary" onclick="Modal.hide()">Đóng</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;

        Modal.show(modalContent);

        // Bind status update form
        document.getElementById('status-update-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            await this.updateOrderStatus(order._id, formData.get('status'));
            Modal.hide();
        });
    },

    async deleteOrder(orderId) {
        if (!confirm('Bạn có chắc chắn muốn xóa đơn hàng này?')) {
            return;
        }

        try {
            Loading.show();

            await API.delete(`/orders/${orderId}`);
            Toast.success('Xóa đơn hàng thành công!');
            this.loadOrders();

        } catch (error) {
            console.error('Delete order error:', error);
            Toast.error('Có lỗi xảy ra khi xóa đơn hàng');
        } finally {
            Loading.hide();
        }
    },

    async exportOrders() {
        try {
            Loading.show();

            const params = new URLSearchParams();
            Object.keys(this.currentFilters).forEach(key => {
                if (this.currentFilters[key] && key !== 'page' && key !== 'limit') {
                    params.append(key, this.currentFilters[key]);
                }
            });

            // Export to CSV
            const response = await fetch(`/api/orders/export/csv?${params}`, {
                headers: {
                    'Authorization': `Bearer ${Storage.get('adminToken')}`
                }
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `orders_${new Date().toISOString().split('T')[0]}.csv`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                Toast.success('Xuất dữ liệu thành công!');
            } else {
                throw new Error('Export failed');
            }

        } catch (error) {
            console.error('Export orders error:', error);
            Toast.error('Có lỗi xảy ra khi xuất dữ liệu');
        } finally {
            Loading.hide();
        }
    },

    getStatusOptions(currentStatus) {
        const statuses = [
            { value: 'pending', text: 'Chờ xử lý' },
            { value: 'confirmed', text: 'Đã xác nhận' },
            { value: 'preparing', text: 'Đang chuẩn bị' },
            { value: 'ready', text: 'Sẵn sàng' },
            { value: 'delivering', text: 'Đang giao' },
            { value: 'delivered', text: 'Đã giao' },
            { value: 'cancelled', text: 'Đã hủy' }
        ];

        return statuses.map(status => 
            `<option value="${status.value}" ${status.value === currentStatus ? 'selected' : ''}>${status.text}</option>`
        ).join('');
    },

    getStatusText(status) {
        const statusMap = {
            'pending': 'Chờ xử lý',
            'confirmed': 'Đã xác nhận',
            'preparing': 'Đang chuẩn bị',
            'ready': 'Sẵn sàng',
            'delivering': 'Đang giao',
            'delivered': 'Đã giao',
            'cancelled': 'Đã hủy'
        };
        return statusMap[status] || status;
    },

    getPaymentMethodText(method) {
        const methodMap = {
            'cod': 'Tiền mặt',
            'bank_transfer': 'Chuyển khoản',
            'e_wallet': 'Ví điện tử',
            'credit_card': 'Thẻ tín dụng'
        };
        return methodMap[method] || method;
    },

    getPaymentStatusText(status) {
        const statusMap = {
            'pending': 'Chờ thanh toán',
            'paid': 'Đã thanh toán',
            'failed': 'Thất bại',
            'refunded': 'Đã hoàn tiền'
        };
        return statusMap[status] || status;
    }
};

// Export for global use
window.AdminOrder = AdminOrder;
