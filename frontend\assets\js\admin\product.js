// Admin Product Management Module
const AdminProduct = {
    products: [],
    currentFilters: {
        search: '',
        category: '',
        status: '',
        page: 1,
        limit: 10
    },

    init() {
        this.bindEvents();
    },

    bindEvents() {
        // Add product button
        const addBtn = document.getElementById('add-product-btn');
        if (addBtn) {
            addBtn.addEventListener('click', () => {
                this.showProductModal();
            });
        }

        // Search and filters
        const searchInput = document.getElementById('product-search');
        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce(() => {
                this.currentFilters.search = searchInput.value;
                this.currentFilters.page = 1;
                this.loadProducts();
            }, 500));
        }

        const categoryFilter = document.getElementById('product-category-filter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', () => {
                this.currentFilters.category = categoryFilter.value;
                this.currentFilters.page = 1;
                this.loadProducts();
            });
        }

        const statusFilter = document.getElementById('product-status-filter');
        if (statusFilter) {
            statusFilter.addEventListener('change', () => {
                this.currentFilters.status = statusFilter.value;
                this.currentFilters.page = 1;
                this.loadProducts();
            });
        }
    },

    async loadProducts() {
        try {
            Loading.show();

            const params = new URLSearchParams();
            Object.keys(this.currentFilters).forEach(key => {
                if (this.currentFilters[key]) {
                    params.append(key, this.currentFilters[key]);
                }
            });

            const response = await API.get('/products', Object.fromEntries(params));
            this.products = response.data;

            this.renderProductsTable(response);
        } catch (error) {
            console.error('Load products error:', error);
            Toast.error('Không thể tải danh sách sản phẩm');
        } finally {
            Loading.hide();
        }
    },

    renderProductsTable(response) {
        const container = document.getElementById('products-table');
        if (!container) return;

        if (this.products.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-utensils"></i>
                    <h3>Chưa có sản phẩm nào</h3>
                    <p>Thêm sản phẩm đầu tiên để bắt đầu</p>
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Hình ảnh</th>
                        <th>Tên món</th>
                        <th>Danh mục</th>
                        <th>Giá</th>
                        <th>Trạng thái</th>
                        <th>Đánh giá</th>
                        <th>Đã bán</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    ${this.products.map(product => `
                        <tr>
                            <td>
                                <img src="${product.primaryImage || '/images/placeholder.jpg'}" 
                                     alt="${product.name}" class="product-thumb">
                            </td>
                            <td>
                                <div class="product-info">
                                    <strong>${product.name}</strong>
                                    ${product.isFeatured ? '<span class="badge badge-featured">Nổi bật</span>' : ''}
                                </div>
                            </td>
                            <td>${this.getCategoryText(product.category)}</td>
                            <td>
                                <div class="price-info">
                                    <strong>${Utils.formatCurrency(product.price)}</strong>
                                    ${product.originalPrice ? `<small class="original-price">${Utils.formatCurrency(product.originalPrice)}</small>` : ''}
                                </div>
                            </td>
                            <td>
                                <span class="status-badge ${product.isAvailable ? 'status-available' : 'status-unavailable'}">
                                    ${product.isAvailable ? 'Có sẵn' : 'Hết hàng'}
                                </span>
                            </td>
                            <td>
                                <div class="rating-info">
                                    <span class="rating-stars">${this.renderStars(product.rating.average)}</span>
                                    <small>(${product.rating.count})</small>
                                </div>
                            </td>
                            <td>${product.soldCount}</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-action btn-edit" onclick="AdminProduct.editProduct('${product._id}')" title="Sửa">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-action btn-toggle" onclick="AdminProduct.toggleProduct('${product._id}')" title="${product.isAvailable ? 'Ẩn' : 'Hiện'}">
                                        <i class="fas fa-${product.isAvailable ? 'eye-slash' : 'eye'}"></i>
                                    </button>
                                    <button class="btn-action btn-delete" onclick="AdminProduct.deleteProduct('${product._id}')" title="Xóa">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
            
            ${this.renderPagination(response.pagination)}
        `;
    },

    renderPagination(pagination) {
        if (!pagination) return '';

        let paginationHTML = '<div class="pagination">';

        // Previous button
        if (pagination.prev) {
            paginationHTML += `<button class="pagination-btn" onclick="AdminProduct.goToPage(${pagination.prev.page})">
                <i class="fas fa-chevron-left"></i> Trước
            </button>`;
        }

        // Current page info
        paginationHTML += `<span class="pagination-info">Trang ${this.currentFilters.page}</span>`;

        // Next button
        if (pagination.next) {
            paginationHTML += `<button class="pagination-btn" onclick="AdminProduct.goToPage(${pagination.next.page})">
                Sau <i class="fas fa-chevron-right"></i>
            </button>`;
        }

        paginationHTML += '</div>';
        return paginationHTML;
    },

    goToPage(page) {
        this.currentFilters.page = page;
        this.loadProducts();
    },

    showProductModal(product = null) {
        const isEdit = !!product;
        const modalContent = `
            <div class="product-modal">
                <div class="modal-header">
                    <h2>${isEdit ? 'Sửa món ăn' : 'Thêm món ăn mới'}</h2>
                    <button class="modal-close" onclick="Modal.hide()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="product-form" class="product-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label>Tên món ăn *</label>
                                <input type="text" name="name" value="${product?.name || ''}" required>
                            </div>
                            <div class="form-group">
                                <label>Danh mục *</label>
                                <select name="category" required>
                                    <option value="">Chọn danh mục</option>
                                    ${this.getCategoryOptions(product?.category)}
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>Mô tả *</label>
                            <textarea name="description" rows="3" required>${product?.description || ''}</textarea>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label>Giá bán *</label>
                                <input type="number" name="price" value="${product?.price || ''}" min="0" required>
                            </div>
                            <div class="form-group">
                                <label>Giá gốc</label>
                                <input type="number" name="originalPrice" value="${product?.originalPrice || ''}" min="0">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label>Thời gian chuẩn bị (phút)</label>
                                <input type="number" name="preparationTime" value="${product?.preparationTime || ''}" min="1">
                            </div>
                            <div class="form-group">
                                <label>Độ cay (0-5)</label>
                                <input type="number" name="spicyLevel" value="${product?.spicyLevel || 0}" min="0" max="5">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>Nguyên liệu</label>
                            <input type="text" name="ingredients" value="${product?.ingredients?.join(', ') || ''}" 
                                   placeholder="Ngăn cách bằng dấu phẩy">
                        </div>
                        
                        <div class="form-group">
                            <label>Tags</label>
                            <input type="text" name="tags" value="${product?.tags?.join(', ') || ''}" 
                                   placeholder="Ngăn cách bằng dấu phẩy">
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="isAvailable" ${product?.isAvailable !== false ? 'checked' : ''}>
                                    Có sẵn
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="isFeatured" ${product?.isFeatured ? 'checked' : ''}>
                                    Món nổi bật
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                ${isEdit ? 'Cập nhật' : 'Thêm món'}
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="Modal.hide()">Hủy</button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        Modal.show(modalContent);

        // Bind form submit
        document.getElementById('product-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveProduct(e.target, isEdit ? product._id : null);
        });
    },

    async saveProduct(form, productId = null) {
        try {
            Loading.show();

            const formData = new FormData(form);
            const productData = {
                name: formData.get('name'),
                description: formData.get('description'),
                price: parseFloat(formData.get('price')),
                originalPrice: formData.get('originalPrice') ? parseFloat(formData.get('originalPrice')) : undefined,
                category: formData.get('category'),
                preparationTime: formData.get('preparationTime') ? parseInt(formData.get('preparationTime')) : undefined,
                spicyLevel: parseInt(formData.get('spicyLevel')),
                ingredients: formData.get('ingredients') ? formData.get('ingredients').split(',').map(s => s.trim()) : [],
                tags: formData.get('tags') ? formData.get('tags').split(',').map(s => s.trim()) : [],
                isAvailable: formData.has('isAvailable'),
                isFeatured: formData.has('isFeatured'),
                images: [{ url: '/images/placeholder.jpg', alt: productData.name, isPrimary: true }]
            };

            let response;
            if (productId) {
                response = await API.put(`/products/${productId}`, productData);
                Toast.success('Cập nhật món ăn thành công!');
            } else {
                response = await API.post('/products', productData);
                Toast.success('Thêm món ăn thành công!');
            }

            Modal.hide();
            this.loadProducts();

        } catch (error) {
            console.error('Save product error:', error);
            Toast.error('Có lỗi xảy ra: ' + error.message);
        } finally {
            Loading.hide();
        }
    },

    async editProduct(productId) {
        try {
            const product = this.products.find(p => p._id === productId);
            if (product) {
                this.showProductModal(product);
            }
        } catch (error) {
            console.error('Edit product error:', error);
            Toast.error('Không thể tải thông tin món ăn');
        }
    },

    async toggleProduct(productId) {
        try {
            const product = this.products.find(p => p._id === productId);
            if (!product) return;

            const response = await API.put(`/products/${productId}`, {
                isAvailable: !product.isAvailable
            });

            Toast.success(`${product.isAvailable ? 'Ẩn' : 'Hiện'} món ăn thành công!`);
            this.loadProducts();

        } catch (error) {
            console.error('Toggle product error:', error);
            Toast.error('Có lỗi xảy ra khi cập nhật trạng thái');
        }
    },

    async deleteProduct(productId) {
        if (!confirm('Bạn có chắc chắn muốn xóa món ăn này?')) {
            return;
        }

        try {
            Loading.show();

            await API.delete(`/products/${productId}`);
            Toast.success('Xóa món ăn thành công!');
            this.loadProducts();

        } catch (error) {
            console.error('Delete product error:', error);
            Toast.error('Có lỗi xảy ra khi xóa món ăn');
        } finally {
            Loading.hide();
        }
    },

    getCategoryOptions(selectedCategory = '') {
        const categories = [
            { value: 'appetizer', text: 'Khai vị' },
            { value: 'main-course', text: 'Món chính' },
            { value: 'dessert', text: 'Tráng miệng' },
            { value: 'beverage', text: 'Đồ uống' },
            { value: 'soup', text: 'Súp' },
            { value: 'salad', text: 'Salad' },
            { value: 'seafood', text: 'Hải sản' },
            { value: 'vegetarian', text: 'Chay' },
            { value: 'fast-food', text: 'Đồ ăn nhanh' },
            { value: 'traditional', text: 'Món truyền thống' }
        ];

        return categories.map(cat => 
            `<option value="${cat.value}" ${cat.value === selectedCategory ? 'selected' : ''}>${cat.text}</option>`
        ).join('');
    },

    getCategoryText(category) {
        const categoryMap = {
            'appetizer': 'Khai vị',
            'main-course': 'Món chính',
            'dessert': 'Tráng miệng',
            'beverage': 'Đồ uống',
            'soup': 'Súp',
            'salad': 'Salad',
            'seafood': 'Hải sản',
            'vegetarian': 'Chay',
            'fast-food': 'Đồ ăn nhanh',
            'traditional': 'Món truyền thống'
        };
        return categoryMap[category] || category;
    },

    renderStars(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

        let starsHTML = '';
        
        for (let i = 0; i < fullStars; i++) {
            starsHTML += '<i class="fas fa-star"></i>';
        }
        
        if (hasHalfStar) {
            starsHTML += '<i class="fas fa-star-half-alt"></i>';
        }
        
        for (let i = 0; i < emptyStars; i++) {
            starsHTML += '<i class="far fa-star"></i>';
        }

        return starsHTML;
    }
};

// Export for global use
window.AdminProduct = AdminProduct;
