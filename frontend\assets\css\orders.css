/* Orders Page Styles */

.orders-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.orders-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.orders-header h1 {
    color: #333;
    font-size: 28px;
    font-weight: 600;
    margin: 0;
}

.orders-filters {
    display: flex;
    gap: 15px;
    align-items: center;
}

.filter-select {
    padding: 10px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Orders List */
.orders-list {
    display: grid;
    gap: 20px;
}

.order-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
}

.order-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.order-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.order-number {
    font-size: 18px;
    font-weight: 700;
    font-family: 'Courier New', monospace;
}

.order-date {
    font-size: 14px;
    opacity: 0.9;
}

.order-body {
    padding: 25px;
}

.order-info {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.info-item {
    text-align: center;
}

.info-label {
    color: #666;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    margin-bottom: 5px;
}

.info-value {
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.info-value.price {
    color: #28a745;
    font-size: 18px;
}

/* Order Status */
.order-status {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-confirmed {
    background: #d1ecf1;
    color: #0c5460;
}

.status-preparing {
    background: #ffeaa7;
    color: #6c5ce7;
}

.status-ready {
    background: #a29bfe;
    color: white;
}

.status-delivering {
    background: #fd79a8;
    color: white;
}

.status-delivered {
    background: #00b894;
    color: white;
}

.status-cancelled {
    background: #fab1a0;
    color: #e17055;
}

/* Order Items */
.order-items {
    margin-bottom: 20px;
}

.order-items h4 {
    color: #333;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
}

.items-list {
    display: grid;
    gap: 10px;
}

.order-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
}

.item-image {
    width: 50px;
    height: 50px;
    border-radius: 6px;
    object-fit: cover;
}

.item-details {
    flex: 1;
}

.item-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 3px;
}

.item-quantity {
    color: #666;
    font-size: 14px;
}

.item-price {
    font-weight: 600;
    color: #333;
}

/* Order Actions */
.order-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.order-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
    transform: translateY(-2px);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-2px);
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
    transform: translateY(-2px);
}

/* Order Timeline */
.order-timeline {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.timeline-item {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    position: relative;
}

.timeline-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 15px;
    top: 30px;
    width: 2px;
    height: 20px;
    background: #e9ecef;
}

.timeline-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: white;
    background: #e9ecef;
}

.timeline-item.completed .timeline-icon {
    background: #28a745;
}

.timeline-item.current .timeline-icon {
    background: #007bff;
}

.timeline-content {
    flex: 1;
}

.timeline-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 3px;
}

.timeline-time {
    color: #666;
    font-size: 12px;
}

/* Empty State */
.empty-orders {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-orders i {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 20px;
}

.empty-orders h3 {
    margin-bottom: 10px;
    color: #333;
}

.empty-orders p {
    margin-bottom: 30px;
}

.empty-orders .btn {
    padding: 12px 30px;
    background: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.empty-orders .btn:hover {
    background: #0056b3;
    transform: translateY(-2px);
}

/* Loading State */
.orders-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 60px 20px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e9ecef;
    border-top-color: #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .orders-container {
        padding: 15px;
    }
    
    .orders-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 20px;
    }
    
    .orders-filters {
        width: 100%;
        justify-content: space-between;
    }
    
    .order-info {
        grid-template-columns: 1fr;
        gap: 15px;
        text-align: left;
    }
    
    .order-actions {
        flex-direction: column;
    }
    
    .order-btn {
        justify-content: center;
    }
    
    .order-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .orders-header h1 {
        font-size: 24px;
    }
    
    .order-header {
        padding: 15px;
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .order-body {
        padding: 20px;
    }
    
    .filter-select {
        width: 100%;
    }
    
    .orders-filters {
        flex-direction: column;
        gap: 10px;
    }
}
