const express = require('express');
const {
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  getFeaturedProducts,
  getProductsByCategory
} = require('../controllers/productController');

const { protect, optionalAuth } = require('../middlewares/authMiddleware');
const { isAdmin } = require('../middlewares/roleMiddleware');

const {
  validateCreateProduct,
  validateUpdateProduct,
  validateProductQuery,
  handleValidationErrors
} = require('../validators/productValidator');

const router = express.Router();

// Public routes
router.get('/', validateProductQuery, handleValidationErrors, optionalAuth, getProducts);
router.get('/featured', optionalAuth, getFeaturedProducts);
router.get('/category/:category', optionalAuth, getProductsByCategory);
router.get('/:id', optionalAuth, getProduct);

// Protected routes (Admin only)
router.post('/', protect, isAdmin, validateCreateProduct, handleValidationErrors, createProduct);
router.put('/:id', protect, isAdmin, validateUpdateProduct, handleValidationErrors, updateProduct);
router.delete('/:id', protect, isAdmin, deleteProduct);

module.exports = router;
