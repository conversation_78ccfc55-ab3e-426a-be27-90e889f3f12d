// Orders Page Module
const Orders = {
    orders: [],
    currentFilter: 'all',

    init() {
        this.bindEvents();
        this.loadOrders();
    },

    bindEvents() {
        // Filter dropdown
        const filterSelect = document.getElementById('orders-filter');
        if (filterSelect) {
            filterSelect.addEventListener('change', (e) => {
                this.currentFilter = e.target.value;
                this.filterOrders();
            });
        }

        // Refresh button
        const refreshBtn = document.getElementById('refresh-orders-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadOrders();
            });
        }
    },

    async loadOrders() {
        try {
            Loading.show();

            // Check if user is logged in
            const token = Storage.get('token');
            if (!token) {
                this.showLoginRequired();
                return;
            }

            const response = await API.get('/orders/me');
            this.orders = response.data;
            this.renderOrders();

        } catch (error) {
            console.error('Load orders error:', error);
            if (error.status === 401) {
                this.showLoginRequired();
            } else {
                Toast.error('Không thể tải danh sách đơn hàng');
            }
        } finally {
            Loading.hide();
        }
    },

    renderOrders() {
        const container = document.getElementById('orders-list');
        if (!container) return;

        if (this.orders.length === 0) {
            this.showEmptyOrders();
            return;
        }

        container.innerHTML = this.orders.map(order => this.renderOrderCard(order)).join('');
    },

    renderOrderCard(order) {
        return `
            <div class="order-card">
                <div class="order-header">
                    <div class="order-number">#${order.orderNumber}</div>
                    <div class="order-date">${Utils.formatDate(order.createdAt)}</div>
                </div>
                
                <div class="order-body">
                    <div class="order-info">
                        <div class="info-item">
                            <div class="info-label">Trạng thái</div>
                            <div class="info-value">
                                <span class="order-status status-${order.status}">
                                    ${this.getStatusText(order.status)}
                                </span>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Thanh toán</div>
                            <div class="info-value">${this.getPaymentMethodText(order.paymentMethod)}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Tổng tiền</div>
                            <div class="info-value price">${Utils.formatCurrency(order.total)}</div>
                        </div>
                    </div>
                    
                    <div class="order-items">
                        <h4>Món ăn đã đặt (${order.items.length} món)</h4>
                        <div class="items-list">
                            ${order.items.slice(0, 3).map(item => `
                                <div class="order-item">
                                    <img src="${item.image}" alt="${item.name}" class="item-image">
                                    <div class="item-details">
                                        <div class="item-name">${item.name}</div>
                                        <div class="item-quantity">x${item.quantity}</div>
                                    </div>
                                    <div class="item-price">${Utils.formatCurrency(item.price * item.quantity)}</div>
                                </div>
                            `).join('')}
                            ${order.items.length > 3 ? `
                                <div class="more-items">+${order.items.length - 3} món khác</div>
                            ` : ''}
                        </div>
                    </div>
                    
                    <div class="order-actions">
                        <button class="order-btn btn-primary" onclick="Orders.viewOrderDetail('${order._id}')">
                            <i class="fas fa-eye"></i> Xem chi tiết
                        </button>
                        
                        ${this.canTrackOrder(order.status) ? `
                            <button class="order-btn btn-secondary" onclick="Orders.trackOrder('${order._id}')">
                                <i class="fas fa-map-marker-alt"></i> Theo dõi
                            </button>
                        ` : ''}
                        
                        ${this.canCancelOrder(order.status) ? `
                            <button class="order-btn btn-danger" onclick="Orders.cancelOrder('${order._id}')">
                                <i class="fas fa-times"></i> Hủy đơn
                            </button>
                        ` : ''}
                        
                        ${this.canReorder(order.status) ? `
                            <button class="order-btn btn-success" onclick="Orders.reorder('${order._id}')">
                                <i class="fas fa-redo"></i> Đặt lại
                            </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    },

    filterOrders() {
        let filteredOrders = this.orders;

        if (this.currentFilter !== 'all') {
            filteredOrders = this.orders.filter(order => order.status === this.currentFilter);
        }

        const container = document.getElementById('orders-list');
        if (container) {
            container.innerHTML = filteredOrders.map(order => this.renderOrderCard(order)).join('');
        }
    },

    async viewOrderDetail(orderId) {
        try {
            Loading.show();

            const response = await API.get(`/orders/${orderId}`);
            const order = response.data;

            this.showOrderDetailModal(order);

        } catch (error) {
            console.error('View order detail error:', error);
            Toast.error('Không thể tải chi tiết đơn hàng');
        } finally {
            Loading.hide();
        }
    },

    showOrderDetailModal(order) {
        const modalContent = `
            <div class="order-detail-modal">
                <div class="modal-header">
                    <h2>Chi tiết đơn hàng #${order.orderNumber}</h2>
                    <button class="modal-close" onclick="Modal.hide()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="order-detail-info">
                        <div class="info-section">
                            <h4>Thông tin đơn hàng</h4>
                            <div class="info-item">
                                <span class="info-label">Mã đơn:</span>
                                <span class="info-value">${order.orderNumber}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Ngày đặt:</span>
                                <span class="info-value">${Utils.formatDate(order.createdAt)}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Trạng thái:</span>
                                <span class="info-value">
                                    <span class="order-status status-${order.status}">
                                        ${this.getStatusText(order.status)}
                                    </span>
                                </span>
                            </div>
                        </div>
                        
                        <div class="info-section">
                            <h4>Thông tin giao hàng</h4>
                            <div class="info-item">
                                <span class="info-label">Tên:</span>
                                <span class="info-value">${order.deliveryInfo.name}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Điện thoại:</span>
                                <span class="info-value">${order.deliveryInfo.phone}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Địa chỉ:</span>
                                <span class="info-value">${order.fullDeliveryAddress}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="order-timeline">
                        <h4>Tiến trình đơn hàng</h4>
                        ${this.renderOrderTimeline(order)}
                    </div>
                    
                    <div class="order-items-detail">
                        <h4>Chi tiết món ăn</h4>
                        ${order.items.map(item => `
                            <div class="item-row">
                                <img src="${item.image}" alt="${item.name}" class="item-image">
                                <div class="item-info">
                                    <div class="item-name">${item.name}</div>
                                    ${item.specialInstructions ? `<div class="item-notes">Ghi chú: ${item.specialInstructions}</div>` : ''}
                                </div>
                                <div class="item-quantity">x${item.quantity}</div>
                                <div class="item-price">${Utils.formatCurrency(item.price)}</div>
                                <div class="item-total">${Utils.formatCurrency(item.price * item.quantity)}</div>
                            </div>
                        `).join('')}
                    </div>
                    
                    <div class="order-summary">
                        <div class="summary-row">
                            <span>Tạm tính:</span>
                            <span>${Utils.formatCurrency(order.subtotal)}</span>
                        </div>
                        <div class="summary-row">
                            <span>Phí giao hàng:</span>
                            <span>${Utils.formatCurrency(order.deliveryFee)}</span>
                        </div>
                        ${order.discount.amount > 0 ? `
                            <div class="summary-row">
                                <span>Giảm giá (${order.discount.code}):</span>
                                <span>-${Utils.formatCurrency(order.discount.amount)}</span>
                            </div>
                        ` : ''}
                        <div class="summary-row total">
                            <span>Tổng cộng:</span>
                            <span>${Utils.formatCurrency(order.total)}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        Modal.show(modalContent);
    },

    renderOrderTimeline(order) {
        const statuses = [
            { key: 'pending', text: 'Chờ xử lý', icon: 'clock' },
            { key: 'confirmed', text: 'Đã xác nhận', icon: 'check' },
            { key: 'preparing', text: 'Đang chuẩn bị', icon: 'utensils' },
            { key: 'ready', text: 'Sẵn sàng', icon: 'bell' },
            { key: 'delivering', text: 'Đang giao', icon: 'truck' },
            { key: 'delivered', text: 'Đã giao', icon: 'check-circle' }
        ];

        const currentStatusIndex = statuses.findIndex(s => s.key === order.status);

        return statuses.map((status, index) => {
            const isCompleted = index <= currentStatusIndex;
            const isCurrent = index === currentStatusIndex;
            
            return `
                <div class="timeline-item ${isCompleted ? 'completed' : ''} ${isCurrent ? 'current' : ''}">
                    <div class="timeline-icon">
                        <i class="fas fa-${status.icon}"></i>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-title">${status.text}</div>
                        ${isCompleted ? `<div class="timeline-time">${Utils.formatDate(order.updatedAt)}</div>` : ''}
                    </div>
                </div>
            `;
        }).join('');
    },

    async trackOrder(orderId) {
        // Mock tracking - in real app would show map/GPS tracking
        Toast.info('Tính năng theo dõi đơn hàng sẽ được cập nhật sớm');
    },

    async cancelOrder(orderId) {
        if (!confirm('Bạn có chắc chắn muốn hủy đơn hàng này?')) {
            return;
        }

        try {
            Loading.show();

            await API.put(`/orders/${orderId}/cancel`);
            Toast.success('Đã hủy đơn hàng thành công');
            this.loadOrders();

        } catch (error) {
            console.error('Cancel order error:', error);
            Toast.error('Không thể hủy đơn hàng');
        } finally {
            Loading.hide();
        }
    },

    async reorder(orderId) {
        try {
            Loading.show();

            const response = await API.get(`/orders/${orderId}`);
            const order = response.data;

            // Add items to cart
            order.items.forEach(item => {
                Cart.addItem({
                    productId: item.productId,
                    name: item.name,
                    price: item.price,
                    image: item.image,
                    quantity: item.quantity,
                    variant: item.variant
                });
            });

            Toast.success('Đã thêm các món vào giỏ hàng');
            Header.updateCartCount();

        } catch (error) {
            console.error('Reorder error:', error);
            Toast.error('Không thể đặt lại đơn hàng');
        } finally {
            Loading.hide();
        }
    },

    canTrackOrder(status) {
        return ['confirmed', 'preparing', 'ready', 'delivering'].includes(status);
    },

    canCancelOrder(status) {
        return ['pending', 'confirmed'].includes(status);
    },

    canReorder(status) {
        return ['delivered', 'cancelled'].includes(status);
    },

    getStatusText(status) {
        const statusMap = {
            'pending': 'Chờ xử lý',
            'confirmed': 'Đã xác nhận',
            'preparing': 'Đang chuẩn bị',
            'ready': 'Sẵn sàng',
            'delivering': 'Đang giao',
            'delivered': 'Đã giao',
            'cancelled': 'Đã hủy'
        };
        return statusMap[status] || status;
    },

    getPaymentMethodText(method) {
        const methodMap = {
            'cod': 'Tiền mặt',
            'bank_transfer': 'Chuyển khoản',
            'e_wallet': 'Ví điện tử',
            'credit_card': 'Thẻ tín dụng'
        };
        return methodMap[method] || method;
    },

    showEmptyOrders() {
        const container = document.getElementById('orders-list');
        if (container) {
            container.innerHTML = `
                <div class="empty-orders">
                    <i class="fas fa-shopping-bag"></i>
                    <h3>Chưa có đơn hàng nào</h3>
                    <p>Bạn chưa đặt đơn hàng nào. Hãy khám phá menu của chúng tôi!</p>
                    <a href="/" class="btn">Đặt hàng ngay</a>
                </div>
            `;
        }
    },

    showLoginRequired() {
        const container = document.getElementById('orders-container');
        if (container) {
            container.innerHTML = `
                <div class="login-required">
                    <i class="fas fa-user-lock"></i>
                    <h3>Vui lòng đăng nhập</h3>
                    <p>Bạn cần đăng nhập để xem danh sách đơn hàng</p>
                    <button onclick="Auth.showLoginModal()" class="btn btn-primary">Đăng nhập</button>
                </div>
            `;
        }
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    Orders.init();
});

// Export for global use
window.Orders = Orders;
