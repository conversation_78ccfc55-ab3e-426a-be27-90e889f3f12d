<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Na Food - Test API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        button {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #e55a2b;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>🍜 Na Food - API Test</h1>
    
    <div class="test-section">
        <h2>🔍 Health Check</h2>
        <button onclick="testHealth()">Test Health API</button>
        <div id="health-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🍽 Products API</h2>
        <button onclick="testProducts()">Get All Products</button>
        <button onclick="testFeaturedProducts()">Get Featured Products</button>
        <div id="products-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🔐 Authentication</h2>
        <button onclick="testLogin()">Test Admin Login</button>
        <button onclick="testRegister()">Test User Register</button>
        <div id="auth-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📊 Server Status</h2>
        <div id="server-status">
            <p><strong>Backend:</strong> <span id="backend-status">Checking...</span></p>
            <p><strong>Database:</strong> <span id="db-status">Checking...</span></p>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function testHealth() {
            const result = document.getElementById('health-result');
            result.textContent = 'Testing...';
            
            const response = await makeRequest(`${API_BASE}/health`);
            
            if (response.success) {
                result.className = 'result success';
                result.textContent = `✅ Health Check Passed!\n${JSON.stringify(response.data, null, 2)}`;
                document.getElementById('backend-status').textContent = '✅ Online';
            } else {
                result.className = 'result error';
                result.textContent = `❌ Health Check Failed!\n${response.error || JSON.stringify(response.data, null, 2)}`;
                document.getElementById('backend-status').textContent = '❌ Offline';
            }
        }

        async function testProducts() {
            const result = document.getElementById('products-result');
            result.textContent = 'Loading products...';
            
            const response = await makeRequest(`${API_BASE}/products`);
            
            if (response.success) {
                result.className = 'result success';
                result.textContent = `✅ Products loaded successfully!\nCount: ${response.data.count}\nTotal: ${response.data.total}\n\n${JSON.stringify(response.data.data.slice(0, 2), null, 2)}`;
                document.getElementById('db-status').textContent = '✅ Connected';
            } else {
                result.className = 'result error';
                result.textContent = `❌ Failed to load products!\n${response.error || JSON.stringify(response.data, null, 2)}`;
            }
        }

        async function testFeaturedProducts() {
            const result = document.getElementById('products-result');
            result.textContent = 'Loading featured products...';
            
            const response = await makeRequest(`${API_BASE}/products/featured`);
            
            if (response.success) {
                result.className = 'result success';
                result.textContent = `✅ Featured products loaded!\nCount: ${response.data.count}\n\n${JSON.stringify(response.data.data, null, 2)}`;
            } else {
                result.className = 'result error';
                result.textContent = `❌ Failed to load featured products!\n${response.error || JSON.stringify(response.data, null, 2)}`;
            }
        }

        async function testLogin() {
            const result = document.getElementById('auth-result');
            result.textContent = 'Testing admin login...';
            
            const response = await makeRequest(`${API_BASE}/auth/login`, {
                method: 'POST',
                body: JSON.stringify({
                    email: '<EMAIL>',
                    password: 'admin123'
                })
            });
            
            if (response.success) {
                result.className = 'result success';
                result.textContent = `✅ Admin login successful!\nUser: ${response.data.user.name}\nRole: ${response.data.user.role}\nToken: ${response.data.token.substring(0, 20)}...`;
                localStorage.setItem('token', response.data.token);
            } else {
                result.className = 'result error';
                result.textContent = `❌ Admin login failed!\n${response.error || JSON.stringify(response.data, null, 2)}`;
            }
        }

        async function testRegister() {
            const result = document.getElementById('auth-result');
            result.textContent = 'Testing user registration...';
            
            const testUser = {
                name: 'Test User',
                email: `test${Date.now()}@example.com`,
                password: 'password123',
                confirmPassword: 'password123'
            };
            
            const response = await makeRequest(`${API_BASE}/auth/register`, {
                method: 'POST',
                body: JSON.stringify(testUser)
            });
            
            if (response.success) {
                result.className = 'result success';
                result.textContent = `✅ User registration successful!\nUser: ${response.data.user.name}\nEmail: ${response.data.user.email}\nRole: ${response.data.user.role}`;
            } else {
                result.className = 'result error';
                result.textContent = `❌ User registration failed!\n${response.error || JSON.stringify(response.data, null, 2)}`;
            }
        }

        // Auto-run health check on page load
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>
