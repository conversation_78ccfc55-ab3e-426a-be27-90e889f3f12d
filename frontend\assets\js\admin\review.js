// Admin Review Management Module
const AdminReview = {
    reviews: [],
    currentFilters: {
        search: '',
        status: '',
        rating: '',
        page: 1,
        limit: 20
    },

    init() {
        this.bindEvents();
    },

    bindEvents() {
        // Search and filters
        const searchInput = document.getElementById('review-search');
        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce(() => {
                this.currentFilters.search = searchInput.value;
                this.currentFilters.page = 1;
                this.loadReviews();
            }, 500));
        }

        const statusFilter = document.getElementById('review-status-filter');
        if (statusFilter) {
            statusFilter.addEventListener('change', () => {
                this.currentFilters.status = statusFilter.value;
                this.currentFilters.page = 1;
                this.loadReviews();
            });
        }

        const ratingFilter = document.getElementById('review-rating-filter');
        if (ratingFilter) {
            ratingFilter.addEventListener('change', () => {
                this.currentFilters.rating = ratingFilter.value;
                this.currentFilters.page = 1;
                this.loadReviews();
            });
        }
    },

    async loadReviews() {
        try {
            Loading.show();

            // Mock data for now
            const mockReviews = [
                {
                    _id: '1',
                    user: { name: 'Nguyễn Văn A', email: '<EMAIL>' },
                    product: { name: 'Phở Bò Tái', _id: 'prod1' },
                    rating: 5,
                    title: 'Rất ngon!',
                    comment: 'Phở rất ngon, nước dùng đậm đà, thịt tươi. Sẽ đặt lại.',
                    isApproved: true,
                    isHidden: false,
                    helpfulCount: 12,
                    createdAt: new Date('2024-11-20')
                },
                {
                    _id: '2',
                    user: { name: 'Trần Thị B', email: '<EMAIL>' },
                    product: { name: 'Bánh Mì Thịt Nướng', _id: 'prod2' },
                    rating: 4,
                    title: 'Khá ổn',
                    comment: 'Bánh mì ngon nhưng hơi ít thịt. Giá cả hợp lý.',
                    isApproved: false,
                    isHidden: false,
                    helpfulCount: 3,
                    createdAt: new Date('2024-11-19')
                },
                {
                    _id: '3',
                    user: { name: 'Lê Văn C', email: '<EMAIL>' },
                    product: { name: 'Cơm Tấm Sườn Nướng', _id: 'prod3' },
                    rating: 2,
                    title: 'Không hài lòng',
                    comment: 'Cơm hơi khô, sườn không đậm đà. Giao hàng chậm.',
                    isApproved: false,
                    isHidden: true,
                    helpfulCount: 0,
                    createdAt: new Date('2024-11-18')
                }
            ];

            this.reviews = mockReviews;
            this.renderReviewsTable({ data: mockReviews, total: mockReviews.length });

        } catch (error) {
            console.error('Load reviews error:', error);
            Toast.error('Không thể tải danh sách đánh giá');
        } finally {
            Loading.hide();
        }
    },

    renderReviewsTable(response) {
        const container = document.getElementById('reviews-table');
        if (!container) return;

        if (this.reviews.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-star"></i>
                    <h3>Chưa có đánh giá nào</h3>
                    <p>Đánh giá từ khách hàng sẽ hiển thị ở đây</p>
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Sản phẩm</th>
                        <th>Khách hàng</th>
                        <th>Đánh giá</th>
                        <th>Nội dung</th>
                        <th>Trạng thái</th>
                        <th>Hữu ích</th>
                        <th>Ngày tạo</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    ${this.reviews.map(review => `
                        <tr>
                            <td>
                                <div class="product-info">
                                    <strong>${review.product.name}</strong>
                                </div>
                            </td>
                            <td>
                                <div class="user-info">
                                    <strong>${review.user.name}</strong>
                                    <small>${review.user.email}</small>
                                </div>
                            </td>
                            <td>
                                <div class="rating-info">
                                    <div class="rating-stars">${this.renderStars(review.rating)}</div>
                                    <small>${review.rating}/5</small>
                                </div>
                            </td>
                            <td>
                                <div class="review-content">
                                    ${review.title ? `<strong>${review.title}</strong><br>` : ''}
                                    <span class="review-comment">${Utils.truncate(review.comment, 100)}</span>
                                </div>
                            </td>
                            <td>
                                <div class="review-status">
                                    <span class="status-badge ${this.getStatusClass(review)}">
                                        ${this.getStatusText(review)}
                                    </span>
                                </div>
                            </td>
                            <td>
                                <span class="helpful-count">${review.helpfulCount}</span>
                            </td>
                            <td>${Utils.formatDate(review.createdAt)}</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-action btn-view" onclick="AdminReview.viewReview('${review._id}')" title="Xem chi tiết">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    ${!review.isApproved ? `
                                        <button class="btn-action btn-approve" onclick="AdminReview.approveReview('${review._id}')" title="Duyệt">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    ` : ''}
                                    <button class="btn-action btn-toggle" onclick="AdminReview.toggleReview('${review._id}')" title="${review.isHidden ? 'Hiện' : 'Ẩn'}">
                                        <i class="fas fa-${review.isHidden ? 'eye' : 'eye-slash'}"></i>
                                    </button>
                                    <button class="btn-action btn-delete" onclick="AdminReview.deleteReview('${review._id}')" title="Xóa">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    },

    async viewReview(reviewId) {
        const review = this.reviews.find(r => r._id === reviewId);
        if (!review) return;

        const modalContent = `
            <div class="review-detail-modal">
                <div class="modal-header">
                    <h2>Chi tiết đánh giá</h2>
                    <button class="modal-close" onclick="Modal.hide()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="review-detail">
                        <div class="review-header">
                            <div class="product-info">
                                <h3>${review.product.name}</h3>
                                <div class="rating-display">
                                    <div class="rating-stars">${this.renderStars(review.rating)}</div>
                                    <span class="rating-number">${review.rating}/5</span>
                                </div>
                            </div>
                            <div class="user-info">
                                <strong>${review.user.name}</strong>
                                <small>${review.user.email}</small>
                                <small>${Utils.formatDate(review.createdAt)}</small>
                            </div>
                        </div>
                        
                        <div class="review-content">
                            ${review.title ? `<h4>${review.title}</h4>` : ''}
                            <p>${review.comment}</p>
                        </div>
                        
                        <div class="review-stats">
                            <div class="stat-item">
                                <span class="stat-label">Trạng thái:</span>
                                <span class="status-badge ${this.getStatusClass(review)}">
                                    ${this.getStatusText(review)}
                                </span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Hữu ích:</span>
                                <span>${review.helpfulCount} lượt</span>
                            </div>
                        </div>
                        
                        <div class="review-actions">
                            ${!review.isApproved ? `
                                <button class="btn btn-primary" onclick="AdminReview.approveReview('${review._id}'); Modal.hide();">
                                    <i class="fas fa-check"></i> Duyệt đánh giá
                                </button>
                            ` : ''}
                            <button class="btn btn-secondary" onclick="AdminReview.toggleReview('${review._id}'); Modal.hide();">
                                <i class="fas fa-${review.isHidden ? 'eye' : 'eye-slash'}"></i> 
                                ${review.isHidden ? 'Hiện' : 'Ẩn'} đánh giá
                            </button>
                            <button class="btn btn-danger" onclick="AdminReview.deleteReview('${review._id}'); Modal.hide();">
                                <i class="fas fa-trash"></i> Xóa đánh giá
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        Modal.show(modalContent);
    },

    async approveReview(reviewId) {
        try {
            // Mock approve - replace with real API call
            const review = this.reviews.find(r => r._id === reviewId);
            if (review) {
                review.isApproved = true;
                this.renderReviewsTable({ data: this.reviews, total: this.reviews.length });
                Toast.success('Duyệt đánh giá thành công!');
            }

        } catch (error) {
            console.error('Approve review error:', error);
            Toast.error('Có lỗi xảy ra khi duyệt đánh giá');
        }
    },

    async toggleReview(reviewId) {
        try {
            // Mock toggle - replace with real API call
            const review = this.reviews.find(r => r._id === reviewId);
            if (review) {
                review.isHidden = !review.isHidden;
                this.renderReviewsTable({ data: this.reviews, total: this.reviews.length });
                Toast.success(`${review.isHidden ? 'Ẩn' : 'Hiện'} đánh giá thành công!`);
            }

        } catch (error) {
            console.error('Toggle review error:', error);
            Toast.error('Có lỗi xảy ra khi cập nhật trạng thái');
        }
    },

    async deleteReview(reviewId) {
        if (!confirm('Bạn có chắc chắn muốn xóa đánh giá này?')) {
            return;
        }

        try {
            // Mock delete - replace with real API call
            this.reviews = this.reviews.filter(r => r._id !== reviewId);
            this.renderReviewsTable({ data: this.reviews, total: this.reviews.length });
            Toast.success('Xóa đánh giá thành công!');

        } catch (error) {
            console.error('Delete review error:', error);
            Toast.error('Có lỗi xảy ra khi xóa đánh giá');
        }
    },

    renderStars(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

        let starsHTML = '';
        
        for (let i = 0; i < fullStars; i++) {
            starsHTML += '<i class="fas fa-star"></i>';
        }
        
        if (hasHalfStar) {
            starsHTML += '<i class="fas fa-star-half-alt"></i>';
        }
        
        for (let i = 0; i < emptyStars; i++) {
            starsHTML += '<i class="far fa-star"></i>';
        }

        return starsHTML;
    },

    getStatusClass(review) {
        if (review.isHidden) return 'status-hidden';
        if (!review.isApproved) return 'status-pending';
        return 'status-approved';
    },

    getStatusText(review) {
        if (review.isHidden) return 'Đã ẩn';
        if (!review.isApproved) return 'Chờ duyệt';
        return 'Đã duyệt';
    }
};

// Export for global use
window.AdminReview = AdminReview;
