// Admin Dashboard Module
const AdminDashboard = {
    currentPage: 'dashboard',
    charts: {},

    init() {
        this.bindEvents();
        this.initNavigation();
    },

    bindEvents() {
        // Sidebar navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.getAttribute('data-page');
                this.navigateToPage(page);
            });
        });

        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebar-toggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }

        // Logout button
        const logoutBtn = document.getElementById('admin-logout');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                if (typeof AdminLogin !== 'undefined') {
                    AdminLogin.logout();
                }
            });
        }
    },

    initNavigation() {
        // Set default active page
        this.navigateToPage('dashboard');
    },

    navigateToPage(pageName) {
        // Update active nav link
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        const activeLink = document.querySelector(`[data-page="${pageName}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }

        // Hide all pages
        document.querySelectorAll('.admin-page').forEach(page => {
            page.classList.remove('active');
        });

        // Show target page
        const targetPage = document.getElementById(`${pageName}-page`);
        if (targetPage) {
            targetPage.classList.add('active');
        }

        // Update page title
        const pageTitle = document.getElementById('page-title');
        if (pageTitle) {
            pageTitle.textContent = this.getPageTitle(pageName);
        }

        // Load page data
        this.loadPageData(pageName);
        this.currentPage = pageName;
    },

    getPageTitle(pageName) {
        const titles = {
            'dashboard': 'Dashboard',
            'products': 'Quản lý món ăn',
            'orders': 'Quản lý đơn hàng',
            'users': 'Quản lý người dùng',
            'reviews': 'Quản lý đánh giá',
            'banners': 'Quản lý banner',
            'reports': 'Báo cáo thống kê',
            'export': 'Xuất dữ liệu'
        };
        return titles[pageName] || 'Dashboard';
    },

    async loadPageData(pageName) {
        switch (pageName) {
            case 'dashboard':
                await this.loadDashboardData();
                break;
            case 'products':
                if (typeof AdminProduct !== 'undefined') {
                    AdminProduct.loadProducts();
                }
                break;
            case 'orders':
                if (typeof AdminOrder !== 'undefined') {
                    AdminOrder.loadOrders();
                }
                break;
            case 'users':
                if (typeof AdminUser !== 'undefined') {
                    AdminUser.loadUsers();
                }
                break;
            case 'reviews':
                if (typeof AdminReview !== 'undefined') {
                    AdminReview.loadReviews();
                }
                break;
            case 'banners':
                if (typeof AdminBanner !== 'undefined') {
                    AdminBanner.loadBanners();
                }
                break;
            case 'reports':
                if (typeof AdminReport !== 'undefined') {
                    AdminReport.loadReports();
                }
                break;
        }
    },

    async loadDashboardData() {
        try {
            Loading.show();

            // Load statistics
            await Promise.all([
                this.loadStats(),
                this.loadRecentOrders(),
                this.loadCharts()
            ]);

        } catch (error) {
            console.error('Load dashboard error:', error);
            Toast.error('Không thể tải dữ liệu dashboard');
        } finally {
            Loading.hide();
        }
    },

    async loadStats() {
        try {
            // Mock data for now - replace with real API calls
            const stats = {
                totalOrders: 1250,
                totalRevenue: 45000000,
                totalProducts: 85,
                totalUsers: 320
            };

            // Update stat cards
            document.getElementById('total-orders').textContent = stats.totalOrders.toLocaleString();
            document.getElementById('total-revenue').textContent = Utils.formatCurrency(stats.totalRevenue);
            document.getElementById('total-products').textContent = stats.totalProducts;
            document.getElementById('total-users').textContent = stats.totalUsers;

        } catch (error) {
            console.error('Load stats error:', error);
        }
    },

    async loadRecentOrders() {
        try {
            // Mock data for recent orders
            const recentOrders = [
                {
                    orderNumber: 'NF20241201001',
                    customerName: 'Nguyễn Văn A',
                    total: 125000,
                    status: 'pending',
                    createdAt: new Date()
                },
                {
                    orderNumber: 'NF20241201002',
                    customerName: 'Trần Thị B',
                    total: 89000,
                    status: 'confirmed',
                    createdAt: new Date()
                }
            ];

            const container = document.getElementById('recent-orders-table');
            if (container) {
                container.innerHTML = `
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Mã đơn</th>
                                <th>Khách hàng</th>
                                <th>Tổng tiền</th>
                                <th>Trạng thái</th>
                                <th>Thời gian</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${recentOrders.map(order => `
                                <tr>
                                    <td>${order.orderNumber}</td>
                                    <td>${order.customerName}</td>
                                    <td>${Utils.formatCurrency(order.total)}</td>
                                    <td><span class="status-badge status-${order.status}">${this.getStatusText(order.status)}</span></td>
                                    <td>${Utils.formatDate(order.createdAt)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;
            }

        } catch (error) {
            console.error('Load recent orders error:', error);
        }
    },

    async loadCharts() {
        try {
            // Revenue chart
            this.createRevenueChart();
            
            // Orders chart
            this.createOrdersChart();

        } catch (error) {
            console.error('Load charts error:', error);
        }
    },

    createRevenueChart() {
        const ctx = document.getElementById('revenue-chart');
        if (!ctx) return;

        // Mock data
        const data = {
            labels: ['T1', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'T8', 'T9', 'T10', 'T11', 'T12'],
            datasets: [{
                label: 'Doanh thu (VNĐ)',
                data: [3200000, 4100000, 3800000, 4500000, 5200000, 4800000, 5500000, 6200000, 5800000, 6500000, 7200000, 6800000],
                borderColor: '#ff6b35',
                backgroundColor: 'rgba(255, 107, 53, 0.1)',
                tension: 0.4
            }]
        };

        if (this.charts.revenue) {
            this.charts.revenue.destroy();
        }

        this.charts.revenue = new Chart(ctx, {
            type: 'line',
            data: data,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return Utils.formatCurrency(value);
                            }
                        }
                    }
                }
            }
        });
    },

    createOrdersChart() {
        const ctx = document.getElementById('orders-chart');
        if (!ctx) return;

        // Mock data
        const data = {
            labels: ['Chờ xử lý', 'Đã xác nhận', 'Đang chuẩn bị', 'Đang giao', 'Đã giao', 'Đã hủy'],
            datasets: [{
                data: [25, 45, 30, 15, 180, 8],
                backgroundColor: [
                    '#ffc107',
                    '#17a2b8',
                    '#fd7e14',
                    '#6f42c1',
                    '#28a745',
                    '#dc3545'
                ]
            }]
        };

        if (this.charts.orders) {
            this.charts.orders.destroy();
        }

        this.charts.orders = new Chart(ctx, {
            type: 'doughnut',
            data: data,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    },

    toggleSidebar() {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            sidebar.classList.toggle('collapsed');
        }
    },

    getStatusText(status) {
        const statusMap = {
            'pending': 'Chờ xử lý',
            'confirmed': 'Đã xác nhận',
            'preparing': 'Đang chuẩn bị',
            'ready': 'Sẵn sàng',
            'delivering': 'Đang giao',
            'delivered': 'Đã giao',
            'cancelled': 'Đã hủy'
        };
        return statusMap[status] || status;
    }
};

// Export for global use
window.AdminDashboard = AdminDashboard;
