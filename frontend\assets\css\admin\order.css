/* Admin Order Management Styles */

.order-filters {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-size: 12px;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
}

.filter-group input,
.filter-group select {
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Order Table */
.order-table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.order-row {
    display: grid;
    grid-template-columns: 150px 200px 120px 150px 120px 120px 100px;
    gap: 15px;
    padding: 20px;
    border-bottom: 1px solid #f1f3f4;
    align-items: center;
    transition: all 0.3s ease;
}

.order-row:hover {
    background: #f8f9fa;
}

.order-row.header {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #e9ecef;
}

.order-number {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #007bff;
}

.customer-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.customer-name {
    font-weight: 600;
    color: #333;
}

.customer-phone {
    font-size: 12px;
    color: #666;
}

.order-date {
    font-size: 14px;
    color: #666;
}

.order-total {
    font-weight: 700;
    color: #28a745;
    font-size: 16px;
}

.payment-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.payment-method {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
}

.payment-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.payment-pending {
    background: #fff3cd;
    color: #856404;
}

.payment-paid {
    background: #d4edda;
    color: #155724;
}

.payment-failed {
    background: #f8d7da;
    color: #721c24;
}

/* Order Status */
.order-status {
    position: relative;
}

.status-select {
    padding: 8px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
}

.status-select:focus {
    outline: none;
    border-color: #007bff;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
    border-color: #ffeaa7;
}

.status-confirmed {
    background: #d1ecf1;
    color: #0c5460;
    border-color: #bee5eb;
}

.status-preparing {
    background: #ffeaa7;
    color: #6c5ce7;
    border-color: #fdcb6e;
}

.status-ready {
    background: #a29bfe;
    color: white;
    border-color: #6c5ce7;
}

.status-delivering {
    background: #fd79a8;
    color: white;
    border-color: #e84393;
}

.status-delivered {
    background: #00b894;
    color: white;
    border-color: #00a085;
}

.status-cancelled {
    background: #fab1a0;
    color: #e17055;
    border-color: #e17055;
}

/* Order Actions */
.order-actions {
    display: flex;
    gap: 5px;
}

.action-btn {
    width: 30px;
    height: 30px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.btn-view {
    background: #17a2b8;
    color: white;
}

.btn-view:hover {
    background: #138496;
    transform: scale(1.1);
}

.btn-edit {
    background: #ffc107;
    color: #333;
}

.btn-edit:hover {
    background: #e0a800;
    transform: scale(1.1);
}

.btn-delete {
    background: #dc3545;
    color: white;
}

.btn-delete:hover {
    background: #c82333;
    transform: scale(1.1);
}

/* Order Detail Modal */
.order-detail-modal {
    width: 100%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
}

.order-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.order-detail-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.info-section h4 {
    color: #333;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
}

.info-label {
    color: #666;
    font-weight: 500;
}

.info-value {
    color: #333;
    font-weight: 600;
}

/* Order Items */
.order-items {
    margin-bottom: 30px;
}

.order-items h4 {
    color: #333;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
}

.item-row {
    display: grid;
    grid-template-columns: 60px 1fr 80px 80px 100px;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #f1f3f4;
    align-items: center;
}

.item-row.header {
    font-weight: 600;
    color: #666;
    font-size: 12px;
    text-transform: uppercase;
    border-bottom: 2px solid #e9ecef;
}

.item-image {
    width: 50px;
    height: 50px;
    border-radius: 6px;
    object-fit: cover;
}

.item-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.item-name {
    font-weight: 600;
    color: #333;
}

.item-description {
    font-size: 12px;
    color: #666;
}

.item-quantity {
    text-align: center;
    font-weight: 600;
}

.item-price,
.item-total {
    text-align: right;
    font-weight: 600;
    color: #333;
}

/* Order Summary */
.order-summary {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-top: 20px;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 14px;
}

.summary-row.total {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    border-top: 2px solid #e9ecef;
    padding-top: 15px;
    margin-top: 15px;
}

/* Status Update Form */
.status-update-form {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-top: 20px;
}

.status-update-form h4 {
    margin-bottom: 15px;
    color: #333;
}

.status-form-group {
    margin-bottom: 15px;
}

.status-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.status-form-group select,
.status-form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
}

.status-form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Export Actions */
.export-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.btn-export {
    padding: 10px 15px;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.btn-export:hover {
    background: #218838;
    transform: translateY(-2px);
}

/* Responsive */
@media (max-width: 1024px) {
    .order-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .order-row.header {
        display: none;
    }
    
    .order-detail-info {
        grid-template-columns: 1fr;
    }
    
    .item-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }
}

@media (max-width: 768px) {
    .order-filters {
        grid-template-columns: 1fr;
    }
    
    .export-actions {
        flex-direction: column;
    }
    
    .status-form-actions {
        flex-direction: column;
    }
}
