<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - Na Food</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .error { color: red; }
        .success { color: green; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Na Food Debug Page</h1>
    
    <div class="debug-section">
        <h2>1. API Health Check</h2>
        <button onclick="testHealth()">Test Health API</button>
        <div id="health-result"></div>
    </div>
    
    <div class="debug-section">
        <h2>2. Products API Check</h2>
        <button onclick="testProducts()">Test Products API</button>
        <div id="products-result"></div>
    </div>
    
    <div class="debug-section">
        <h2>3. Frontend Modules Check</h2>
        <button onclick="testModules()">Test Modules</button>
        <div id="modules-result"></div>
    </div>

    <script>
        // API Configuration
        const API_BASE_URL = '/api';

        async function testHealth() {
            const resultDiv = document.getElementById('health-result');
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                const data = await response.json();
                resultDiv.innerHTML = `<div class="success">✅ Health API OK</div><pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Health API Error: ${error.message}</div>`;
            }
        }

        async function testProducts() {
            const resultDiv = document.getElementById('products-result');
            try {
                const response = await fetch(`${API_BASE_URL}/products`);
                const data = await response.json();
                resultDiv.innerHTML = `<div class="success">✅ Products API OK</div><div class="info">Found ${data.data ? data.data.length : 0} products</div><pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Products API Error: ${error.message}</div>`;
            }
        }

        function testModules() {
            const resultDiv = document.getElementById('modules-result');
            let results = [];
            
            // Check if modules are loaded
            const modules = ['API', 'Utils', 'Storage', 'Toast', 'Loading', 'Modal'];
            modules.forEach(module => {
                if (typeof window[module] !== 'undefined') {
                    results.push(`✅ ${module} loaded`);
                } else {
                    results.push(`❌ ${module} not loaded`);
                }
            });
            
            resultDiv.innerHTML = results.map(r => `<div class="${r.includes('✅') ? 'success' : 'error'}">${r}</div>`).join('');
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            console.log('Debug page loaded');
            testHealth();
            setTimeout(testProducts, 1000);
            setTimeout(testModules, 2000);
        });
    </script>
</body>
</html>
