/* Admin Banner Management Styles */

.banner-thumb {
    width: 80px;
    height: 50px;
    border-radius: 6px;
    object-fit: cover;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.banner-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.banner-info strong {
    color: #333;
    font-weight: 600;
}

.banner-info small {
    color: #666;
    font-size: 12px;
    word-break: break-all;
}

.banner-description {
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

/* Order Controls */
.order-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.btn-order {
    width: 30px;
    height: 25px;
    border: none;
    background: #f8f9fa;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #666;
}

.btn-order:hover {
    background: #007bff;
    color: white;
    transform: scale(1.1);
}

.btn-order:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.order-number {
    font-weight: 600;
    color: #333;
    font-size: 16px;
    padding: 5px 0;
}

/* Date Range */
.date-range {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.date-range small {
    font-size: 12px;
    color: #666;
}

/* Banner Modal */
.banner-modal {
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.banner-form {
    padding: 0;
}

.banner-form .form-group {
    margin-bottom: 20px;
}

.banner-form label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 600;
    font-size: 14px;
}

.banner-form input,
.banner-form textarea,
.banner-form select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.banner-form input:focus,
.banner-form textarea:focus,
.banner-form select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.banner-form small {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 12px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-weight: 500;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

/* Banner Preview */
.banner-preview {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.banner-preview h3 {
    margin-bottom: 20px;
    color: #333;
}

.banner-slider {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    height: 200px;
    background: #f8f9fa;
}

.banner-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.banner-slide.active {
    opacity: 1;
}

.banner-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.banner-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: 20px;
}

.banner-overlay h4 {
    margin-bottom: 5px;
    font-size: 18px;
}

.banner-overlay p {
    font-size: 14px;
    opacity: 0.9;
}

.banner-controls {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 15px;
}

.banner-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ddd;
    cursor: pointer;
    transition: all 0.3s ease;
}

.banner-dot.active {
    background: #007bff;
}

/* Banner Statistics */
.banner-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.stat-card .stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #007bff;
    margin-bottom: 5px;
}

.stat-card .stat-label {
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

/* Banner Upload */
.banner-upload {
    border: 2px dashed #e9ecef;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.banner-upload:hover {
    border-color: #007bff;
    background: rgba(0, 123, 255, 0.05);
}

.banner-upload.dragover {
    border-color: #007bff;
    background: rgba(0, 123, 255, 0.1);
}

.upload-icon {
    font-size: 3rem;
    color: #ddd;
    margin-bottom: 15px;
}

.upload-text {
    color: #666;
    margin-bottom: 10px;
}

.upload-hint {
    color: #999;
    font-size: 12px;
}

.banner-upload input[type="file"] {
    display: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .banner-form .form-actions {
        flex-direction: column;
    }
    
    .order-controls {
        flex-direction: row;
        justify-content: center;
    }
    
    .banner-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .banner-slider {
        height: 150px;
    }
}

@media (max-width: 480px) {
    .banner-stats {
        grid-template-columns: 1fr;
    }
    
    .banner-slider {
        height: 120px;
    }
    
    .banner-overlay {
        padding: 15px;
    }
    
    .banner-overlay h4 {
        font-size: 16px;
    }
    
    .banner-overlay p {
        font-size: 12px;
    }
}
