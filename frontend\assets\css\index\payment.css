/* Payment Page Styles */

.payment-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.payment-status {
    text-align: center;
    background: #fff;
    border-radius: 12px;
    padding: 60px 40px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.status-icon {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 30px;
    font-size: 3rem;
}

.status-success {
    background: var(--success-light);
    color: var(--success-color);
}

.status-failed {
    background: var(--danger-light);
    color: var(--danger-color);
}

.status-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--text-dark);
}

.status-message {
    font-size: 1.2rem;
    color: var(--text-muted);
    line-height: 1.6;
    margin-bottom: 40px;
}

/* Payment Header */
.payment-header {
    text-align: center;
    background: #fff;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.payment-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 15px;
}

.payment-header p {
    color: var(--text-muted);
    font-size: 1.1rem;
}

/* Payment Order Summary */
.payment-order-summary {
    background: #fff;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.payment-order-summary h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 20px;
    text-align: center;
}

.order-details {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 25px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f8f9fa;
}

.detail-label {
    font-weight: 600;
    color: var(--text-muted);
}

.detail-value {
    color: var(--text-dark);
    font-weight: 500;
}

.detail-value.price {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--primary-color);
}

/* Payment Methods Section */
.payment-methods-section {
    background: #fff;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.payment-methods-section h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 25px;
}

.payment-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.payment-option {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 20px;
}

.payment-option:hover {
    border-color: var(--primary-color);
    background: #f8f9ff;
}

.payment-option.selected {
    border-color: var(--primary-color);
    background: #f8f9ff;
}

.payment-option input[type="radio"] {
    margin: 0;
    transform: scale(1.2);
}

.payment-method-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-light);
    border-radius: 10px;
    color: var(--primary-color);
    font-size: 1.5rem;
}

.payment-method-info {
    flex: 1;
}

.payment-method-info h4 {
    margin: 0 0 8px 0;
    color: var(--text-dark);
    font-weight: 600;
    font-size: 1.1rem;
}

.payment-method-info p {
    margin: 0;
    color: var(--text-muted);
    font-size: 0.95rem;
}

.payment-method-fee {
    color: var(--success-color);
    font-weight: 600;
    font-size: 1rem;
}

/* Payment Forms */
.payment-form {
    display: none;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 25px;
    margin-top: 20px;
}

.payment-form.active {
    display: block;
}

.payment-form h4 {
    margin-bottom: 20px;
    color: var(--text-dark);
    font-weight: 600;
}

/* Bank Transfer Form */
.bank-info {
    margin-bottom: 30px;
}

.bank-info h5 {
    margin-bottom: 15px;
    color: var(--text-dark);
    font-weight: 600;
}

.bank-details {
    background: white;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

.bank-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f8f9fa;
}

.bank-detail:last-child {
    border-bottom: none;
}

.bank-label {
    font-weight: 600;
    color: var(--text-muted);
}

.bank-value {
    color: var(--text-dark);
    font-weight: 500;
    font-family: monospace;
}

.copy-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
    margin-left: 10px;
}

.copy-btn:hover {
    background: var(--primary-dark);
}

/* QR Section */
.qr-section {
    text-align: center;
}

.qr-section h5 {
    margin-bottom: 20px;
    color: var(--text-dark);
    font-weight: 600;
}

.qr-code {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
}

.qr-placeholder {
    width: 200px;
    height: 200px;
    background: white;
    border: 2px dashed #e9ecef;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    color: var(--text-light);
}

.qr-section p {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Payment Actions */
.payment-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-top: 30px;
}

.payment-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 180px;
    justify-content: center;
}

.btn-pay {
    background: var(--primary-color);
    color: white;
}

.btn-pay:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-cancel {
    background: #6c757d;
    color: white;
}

.btn-cancel:hover {
    background: #5a6268;
}

/* Security Notice */
.security-notice {
    text-align: center;
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.security-notice i {
    color: var(--success-color);
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.security-notice p {
    margin: 0;
    color: var(--text-muted);
    font-size: 0.95rem;
}

/* Payment Error */
.payment-error {
    text-align: center;
    background: #fff;
    border-radius: 12px;
    padding: 60px 40px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.payment-error i {
    font-size: 4rem;
    color: var(--danger-color);
    margin-bottom: 20px;
}

.payment-error h3 {
    margin-bottom: 15px;
    color: var(--text-dark);
}

.payment-error p {
    color: var(--text-muted);
    margin-bottom: 30px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .payment-container {
        padding: 15px;
    }
    
    .payment-status,
    .payment-header {
        padding: 40px 20px;
    }
    
    .status-title {
        font-size: 2rem;
    }
    
    .payment-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .payment-btn {
        width: 100%;
        max-width: 300px;
    }
    
    .payment-option {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .bank-detail {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .copy-btn {
        margin-left: 0;
        margin-top: 5px;
    }
}
