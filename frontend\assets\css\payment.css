/* Payment Page Styles */

.payment-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.payment-header {
    text-align: center;
    margin-bottom: 40px;
}

.payment-header h1 {
    color: #333;
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 10px;
}

.payment-header p {
    color: #666;
    font-size: 16px;
}

/* Payment Status */
.payment-status {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
    margin-bottom: 30px;
}

.status-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40px;
    margin: 0 auto 20px;
}

.status-success .status-icon {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.status-pending .status-icon {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
}

.status-failed .status-icon {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
    color: white;
}

.status-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 10px;
}

.status-success .status-title {
    color: #28a745;
}

.status-pending .status-title {
    color: #ffc107;
}

.status-failed .status-title {
    color: #dc3545;
}

.status-message {
    color: #666;
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 20px;
}

/* Order Summary in Payment */
.payment-order-summary {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.payment-order-summary h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: 600;
}

.order-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f1f3f4;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    color: #666;
    font-weight: 500;
}

.detail-value {
    color: #333;
    font-weight: 600;
}

.detail-value.price {
    color: #28a745;
    font-size: 18px;
}

/* Payment Methods */
.payment-methods-section {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.payment-methods-section h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: 600;
}

.payment-options {
    display: grid;
    gap: 15px;
}

.payment-option {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 20px;
}

.payment-option:hover {
    border-color: #007bff;
    background: rgba(0, 123, 255, 0.05);
}

.payment-option.selected {
    border-color: #007bff;
    background: rgba(0, 123, 255, 0.1);
}

.payment-option input[type="radio"] {
    width: auto;
    margin: 0;
}

.payment-method-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.method-cod .payment-method-icon {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.method-bank .payment-method-icon {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.method-ewallet .payment-method-icon {
    background: linear-gradient(135deg, #17a2b8, #138496);
}

.method-card .payment-method-icon {
    background: linear-gradient(135deg, #6f42c1, #5a32a3);
}

.payment-method-info {
    flex: 1;
}

.payment-method-info h4 {
    color: #333;
    margin-bottom: 5px;
    font-size: 18px;
    font-weight: 600;
}

.payment-method-info p {
    color: #666;
    font-size: 14px;
    margin: 0;
    line-height: 1.4;
}

.payment-method-fee {
    color: #28a745;
    font-weight: 600;
    font-size: 16px;
}

/* Payment Form */
.payment-form {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    display: none;
}

.payment-form.active {
    display: block;
}

.payment-form h4 {
    color: #333;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
}

.card-form {
    display: grid;
    gap: 20px;
}

.card-row {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 15px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 600;
    font-size: 14px;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Bank Transfer Info */
.bank-info {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-top: 20px;
}

.bank-info h5 {
    color: #333;
    margin-bottom: 15px;
    font-weight: 600;
}

.bank-details {
    display: grid;
    gap: 10px;
}

.bank-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.bank-label {
    color: #666;
    font-weight: 500;
}

.bank-value {
    color: #333;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

.copy-btn {
    padding: 4px 8px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    margin-left: 10px;
}

/* QR Code */
.qr-section {
    text-align: center;
    margin-top: 20px;
}

.qr-code {
    width: 200px;
    height: 200px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    margin: 0 auto 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
}

.qr-placeholder {
    color: #ccc;
    font-size: 48px;
}

/* Payment Actions */
.payment-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
}

.payment-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
}

.btn-pay {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.btn-pay:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(40, 167, 69, 0.3);
}

.btn-cancel {
    background: #6c757d;
    color: white;
}

.btn-cancel:hover {
    background: #545b62;
    transform: translateY(-2px);
}

.btn-back {
    background: #007bff;
    color: white;
    text-decoration: none;
}

.btn-back:hover {
    background: #0056b3;
    transform: translateY(-2px);
}

/* Security Notice */
.security-notice {
    background: #e8f4fd;
    border: 1px solid #bee5eb;
    border-radius: 10px;
    padding: 15px;
    margin-top: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.security-notice i {
    color: #17a2b8;
    font-size: 20px;
}

.security-notice p {
    color: #0c5460;
    font-size: 14px;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .payment-container {
        padding: 15px;
    }
    
    .order-details {
        grid-template-columns: 1fr;
    }
    
    .card-row {
        grid-template-columns: 1fr;
    }
    
    .payment-actions {
        flex-direction: column;
    }
    
    .payment-btn {
        justify-content: center;
    }
    
    .payment-option {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .payment-header h1 {
        font-size: 24px;
    }
    
    .status-icon {
        width: 60px;
        height: 60px;
        font-size: 30px;
    }
    
    .status-title {
        font-size: 20px;
    }
    
    .qr-code {
        width: 150px;
        height: 150px;
    }
    
    .payment-method-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
}
