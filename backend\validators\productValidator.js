const { body, query, validationResult } = require('express-validator');

// Validation rules for creating product
const validateCreateProduct = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Product name must be between 2 and 100 characters'),

  body('description')
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Description must be between 10 and 1000 characters'),

  body('price')
    .isFloat({ min: 0 })
    .withMessage('Price must be a positive number'),

  body('originalPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Original price must be a positive number')
    .custom((value, { req }) => {
      if (value && value < req.body.price) {
        throw new Error('Original price must be greater than or equal to current price');
      }
      return true;
    }),

  body('category')
    .isIn([
      'appetizer', 'main-course', 'dessert', 'beverage', 'soup', 
      'salad', 'seafood', 'vegetarian', 'fast-food', 'traditional'
    ])
    .withMessage('Please select a valid category'),

  body('images')
    .isArray({ min: 1 })
    .withMessage('At least one image is required'),

  body('images.*.url')
    .isURL()
    .withMessage('Each image must have a valid URL'),

  body('images.*.alt')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Image alt text cannot exceed 100 characters'),

  body('ingredients')
    .optional()
    .isArray()
    .withMessage('Ingredients must be an array'),

  body('ingredients.*')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Each ingredient must be between 1 and 50 characters'),

  body('nutritionInfo.calories')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Calories must be a positive number'),

  body('nutritionInfo.protein')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Protein must be a positive number'),

  body('nutritionInfo.carbs')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Carbs must be a positive number'),

  body('nutritionInfo.fat')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Fat must be a positive number'),

  body('nutritionInfo.fiber')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Fiber must be a positive number'),

  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),

  body('tags.*')
    .optional()
    .trim()
    .isLength({ min: 1, max: 20 })
    .withMessage('Each tag must be between 1 and 20 characters'),

  body('preparationTime')
    .optional()
    .isInt({ min: 1, max: 300 })
    .withMessage('Preparation time must be between 1 and 300 minutes'),

  body('servingSize')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Serving size cannot exceed 50 characters'),

  body('spicyLevel')
    .optional()
    .isInt({ min: 0, max: 5 })
    .withMessage('Spicy level must be between 0 and 5'),

  body('allergens')
    .optional()
    .isArray()
    .withMessage('Allergens must be an array'),

  body('allergens.*')
    .optional()
    .isIn(['gluten', 'dairy', 'eggs', 'nuts', 'soy', 'shellfish', 'fish'])
    .withMessage('Invalid allergen type'),

  body('isAvailable')
    .optional()
    .isBoolean()
    .withMessage('isAvailable must be a boolean'),

  body('isFeatured')
    .optional()
    .isBoolean()
    .withMessage('isFeatured must be a boolean')
];

// Validation rules for updating product
const validateUpdateProduct = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Product name must be between 2 and 100 characters'),

  body('description')
    .optional()
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Description must be between 10 and 1000 characters'),

  body('price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Price must be a positive number'),

  body('originalPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Original price must be a positive number'),

  body('category')
    .optional()
    .isIn([
      'appetizer', 'main-course', 'dessert', 'beverage', 'soup', 
      'salad', 'seafood', 'vegetarian', 'fast-food', 'traditional'
    ])
    .withMessage('Please select a valid category'),

  body('images')
    .optional()
    .isArray({ min: 1 })
    .withMessage('At least one image is required'),

  body('images.*.url')
    .optional()
    .isURL()
    .withMessage('Each image must have a valid URL'),

  body('preparationTime')
    .optional()
    .isInt({ min: 1, max: 300 })
    .withMessage('Preparation time must be between 1 and 300 minutes'),

  body('spicyLevel')
    .optional()
    .isInt({ min: 0, max: 5 })
    .withMessage('Spicy level must be between 0 and 5'),

  body('isAvailable')
    .optional()
    .isBoolean()
    .withMessage('isAvailable must be a boolean'),

  body('isFeatured')
    .optional()
    .isBoolean()
    .withMessage('isFeatured must be a boolean')
];

// Validation rules for product query parameters
const validateProductQuery = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),

  query('sort')
    .optional()
    .isIn(['name', '-name', 'price', '-price', 'createdAt', '-createdAt', 'rating', '-rating', 'soldCount', '-soldCount'])
    .withMessage('Invalid sort parameter'),

  query('category')
    .optional()
    .isIn([
      'appetizer', 'main-course', 'dessert', 'beverage', 'soup', 
      'salad', 'seafood', 'vegetarian', 'fast-food', 'traditional'
    ])
    .withMessage('Invalid category'),

  query('minPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Minimum price must be a positive number'),

  query('maxPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Maximum price must be a positive number'),

  query('isAvailable')
    .optional()
    .isBoolean()
    .withMessage('isAvailable must be a boolean'),

  query('isFeatured')
    .optional()
    .isBoolean()
    .withMessage('isFeatured must be a boolean'),

  query('search')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters')
];

// Middleware to handle validation errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => ({
      field: error.param,
      message: error.msg,
      value: error.value
    }));

    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errorMessages
    });
  }

  next();
};

module.exports = {
  validateCreateProduct,
  validateUpdateProduct,
  validateProductQuery,
  handleValidationErrors
};
