# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
MONGO_URI=mongodb://localhost:27017/nafood
# For MongoDB Atlas:
# MONGO_URI=mongodb+srv://username:<EMAIL>/nafood

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRE=7d
JWT_COOKIE_EXPIRE=7

# File Upload Configuration
MAX_FILE_SIZE=5000000
FILE_UPLOAD_PATH=./uploads

# Email Configuration (for future use)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_EMAIL=<EMAIL>
SMTP_PASSWORD=your-app-password

# Payment Configuration (for future use)
STRIPE_SECRET_KEY=your-stripe-secret-key
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret

# Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_SALT_ROUNDS=10

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:3000
