/* Review Styles */

.reviews-section {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    margin-top: 30px;
}

.reviews-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.reviews-header h3 {
    color: #333;
    font-size: 24px;
    font-weight: 600;
    margin: 0;
}

.reviews-stats {
    display: flex;
    align-items: center;
    gap: 20px;
}

.rating-summary {
    display: flex;
    align-items: center;
    gap: 10px;
}

.rating-average {
    font-size: 28px;
    font-weight: 700;
    color: #333;
}

.rating-stars {
    color: #ffc107;
    font-size: 20px;
}

.rating-count {
    color: #666;
    font-size: 14px;
}

.write-review-btn {
    padding: 12px 24px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.write-review-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
}

/* Rating Breakdown */
.rating-breakdown {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 30px;
}

.rating-breakdown h4 {
    color: #333;
    margin-bottom: 15px;
    font-weight: 600;
}

.rating-bars {
    display: grid;
    gap: 10px;
}

.rating-bar {
    display: flex;
    align-items: center;
    gap: 10px;
}

.bar-label {
    min-width: 60px;
    color: #666;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.bar-progress {
    flex: 1;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #ffc107, #fd7e14);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.bar-count {
    min-width: 40px;
    color: #666;
    font-size: 14px;
    text-align: right;
}

/* Reviews List */
.reviews-list {
    display: grid;
    gap: 20px;
}

.review-item {
    padding: 25px;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.review-item:hover {
    border-color: #007bff;
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.1);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.reviewer-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.reviewer-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 18px;
}

.reviewer-details h5 {
    color: #333;
    margin-bottom: 5px;
    font-weight: 600;
}

.review-rating {
    color: #ffc107;
    font-size: 16px;
    margin-bottom: 3px;
}

.review-date {
    color: #666;
    font-size: 12px;
}

.review-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.btn-helpful {
    background: #e8f4fd;
    color: #007bff;
}

.btn-helpful:hover {
    background: #007bff;
    color: white;
}

.btn-helpful.active {
    background: #007bff;
    color: white;
}

.btn-report {
    background: #fff5f5;
    color: #dc3545;
}

.btn-report:hover {
    background: #dc3545;
    color: white;
}

.review-content {
    margin-bottom: 15px;
}

.review-title {
    color: #333;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 16px;
}

.review-text {
    color: #666;
    line-height: 1.6;
    font-size: 14px;
}

.review-images {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.review-image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    object-fit: cover;
    cursor: pointer;
    transition: all 0.3s ease;
}

.review-image:hover {
    transform: scale(1.05);
}

.review-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f1f3f4;
}

.helpful-count {
    color: #666;
    font-size: 14px;
}

.verified-purchase {
    background: #d4edda;
    color: #155724;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Review Form Modal */
.review-modal {
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.review-form {
    padding: 0;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 600;
    font-size: 14px;
}

.rating-input {
    display: flex;
    gap: 5px;
    margin-bottom: 10px;
}

.star-input {
    font-size: 24px;
    color: #ddd;
    cursor: pointer;
    transition: all 0.3s ease;
}

.star-input:hover,
.star-input.active {
    color: #ffc107;
    transform: scale(1.1);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.image-upload {
    border: 2px dashed #e9ecef;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.image-upload:hover {
    border-color: #007bff;
    background: rgba(0, 123, 255, 0.05);
}

.image-upload input {
    display: none;
}

.upload-text {
    color: #666;
    margin-bottom: 10px;
}

.upload-hint {
    color: #999;
    font-size: 12px;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.form-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-submit {
    background: #28a745;
    color: white;
}

.btn-submit:hover {
    background: #218838;
    transform: translateY(-2px);
}

.btn-cancel {
    background: #6c757d;
    color: white;
}

.btn-cancel:hover {
    background: #545b62;
    transform: translateY(-2px);
}

/* Review Filters */
.review-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    align-items: center;
}

.filter-select {
    padding: 8px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    cursor: pointer;
}

.filter-select:focus {
    outline: none;
    border-color: #007bff;
}

/* Empty Reviews */
.empty-reviews {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-reviews i {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 20px;
}

.empty-reviews h4 {
    margin-bottom: 10px;
    color: #333;
}

.empty-reviews p {
    margin-bottom: 30px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .reviews-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 20px;
    }
    
    .reviews-stats {
        width: 100%;
        justify-content: space-between;
    }
    
    .review-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .review-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .review-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .form-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .reviews-section {
        padding: 20px;
    }
    
    .reviewer-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .reviewer-avatar {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
    
    .rating-input {
        justify-content: center;
    }
    
    .star-input {
        font-size: 20px;
    }
}
