/* Review Styles */

.review-modal {
    max-width: 600px;
    width: 90%;
}

.review-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.rating-input {
    display: flex;
    gap: 5px;
    margin-bottom: 10px;
}

.star-input {
    font-size: 2rem;
    cursor: pointer;
    transition: color 0.3s ease;
    color: #ddd;
}

.star-input:hover,
.star-input.active {
    color: #ffc107;
}

.rating-text {
    font-weight: 600;
    color: var(--primary-color);
}

.image-upload {
    border: 2px dashed #e9ecef;
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.image-upload:hover {
    border-color: var(--primary-color);
}

.upload-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    color: var(--text-muted);
}

.upload-text i {
    font-size: 2rem;
    color: var(--primary-color);
}

.upload-hint {
    font-size: 0.85rem;
    color: var(--text-light);
    margin-top: 5px;
}

.image-preview {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-top: 15px;
}

.preview-image {
    position: relative;
    width: 80px;
    height: 80px;
}

.preview-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 6px;
}

.remove-image {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 20px;
    height: 20px;
    background: var(--danger-color);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 20px;
}

.form-btn {
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-submit {
    background: var(--primary-color);
    color: white;
}

.btn-submit:hover {
    background: var(--primary-dark);
}

.btn-cancel {
    background: #6c757d;
    color: white;
}

.btn-cancel:hover {
    background: #5a6268;
}

/* Reviews List */
.reviews-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.review-item {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.reviewer-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.reviewer-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.2rem;
}

.reviewer-details h5 {
    margin: 0 0 5px 0;
    color: var(--text-dark);
    font-weight: 600;
}

.review-rating {
    margin: 5px 0;
}

.review-rating i {
    color: #ffc107;
    font-size: 0.9rem;
}

.review-date {
    color: var(--text-muted);
    font-size: 0.85rem;
}

.review-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    background: none;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 6px 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.85rem;
}

.btn-helpful {
    color: var(--text-muted);
}

.btn-helpful.active {
    background: var(--primary-light);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-report {
    color: var(--danger-color);
}

.btn-report:hover {
    background: var(--danger-light);
    border-color: var(--danger-color);
}

.review-content {
    margin-bottom: 15px;
}

.review-title {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 8px;
    font-size: 1.1rem;
}

.review-text {
    color: var(--text-dark);
    line-height: 1.6;
    margin-bottom: 15px;
}

.review-images {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.review-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 6px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.review-image:hover {
    transform: scale(1.05);
}

.review-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    color: var(--text-muted);
}

.verified-purchase {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--success-color);
    font-weight: 600;
}

.verified-purchase i {
    font-size: 0.9rem;
}

.empty-reviews {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-muted);
}

.empty-reviews i {
    font-size: 3rem;
    margin-bottom: 20px;
    color: var(--text-light);
}

.empty-reviews h4 {
    margin-bottom: 10px;
    color: var(--text-dark);
}

/* Review Stats */
.review-stats {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
}

.rating-summary {
    text-align: center;
    margin-bottom: 25px;
}

.rating-average {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.rating-stars {
    margin-bottom: 10px;
}

.rating-stars i {
    color: #ffc107;
    font-size: 1.5rem;
    margin: 0 2px;
}

.rating-count {
    color: var(--text-muted);
    font-size: 1.1rem;
}

.rating-breakdown {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.rating-bar {
    display: flex;
    align-items: center;
    gap: 15px;
}

.bar-label {
    display: flex;
    align-items: center;
    gap: 5px;
    min-width: 40px;
    font-weight: 600;
    color: var(--text-dark);
}

.bar-label i {
    color: #ffc107;
    font-size: 0.9rem;
}

.bar-progress {
    flex: 1;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.bar-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.bar-count {
    min-width: 30px;
    text-align: right;
    color: var(--text-muted);
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .review-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .review-actions {
        align-self: flex-start;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .rating-breakdown {
        gap: 15px;
    }
}
