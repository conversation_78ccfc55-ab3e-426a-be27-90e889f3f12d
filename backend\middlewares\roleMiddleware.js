// Grant access to specific roles
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to access this route'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: `User role ${req.user.role} is not authorized to access this route`
      });
    }

    next();
  };
};

// Check if user is admin
const isAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Not authorized to access this route'
    });
  }

  if (req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Admin access required'
    });
  }

  next();
};

// Check if user is staff or admin
const isStaffOrAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Not authorized to access this route'
    });
  }

  if (!['staff', 'admin'].includes(req.user.role)) {
    return res.status(403).json({
      success: false,
      error: 'Staff or Admin access required'
    });
  }

  next();
};

// Check if user owns the resource or is admin
const isOwnerOrAdmin = (resourceUserField = 'user') => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to access this route'
      });
    }

    // Admin can access everything
    if (req.user.role === 'admin') {
      return next();
    }

    // Check if user owns the resource
    const resourceUserId = req.resource ? req.resource[resourceUserField] : req.params.userId;
    
    if (resourceUserId && resourceUserId.toString() === req.user._id.toString()) {
      return next();
    }

    return res.status(403).json({
      success: false,
      error: 'Not authorized to access this resource'
    });
  };
};

// Middleware to check resource ownership
const checkResourceOwnership = (Model, resourceUserField = 'user') => {
  return async (req, res, next) => {
    try {
      const resource = await Model.findById(req.params.id);
      
      if (!resource) {
        return res.status(404).json({
          success: false,
          error: 'Resource not found'
        });
      }

      req.resource = resource;
      next();
    } catch (error) {
      return res.status(500).json({
        success: false,
        error: 'Server error while checking resource ownership'
      });
    }
  };
};

// Role hierarchy check
const hasPermission = (requiredPermissions) => {
  const rolePermissions = {
    user: ['read_own_profile', 'update_own_profile', 'create_order', 'read_own_orders', 'cancel_own_order', 'create_review'],
    staff: ['read_orders', 'update_order_status', 'read_products', 'read_users'],
    admin: ['*'] // Admin has all permissions
  };

  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to access this route'
      });
    }

    const userPermissions = rolePermissions[req.user.role] || [];
    
    // Admin has all permissions
    if (userPermissions.includes('*')) {
      return next();
    }

    // Check if user has required permissions
    const hasRequiredPermission = requiredPermissions.some(permission => 
      userPermissions.includes(permission)
    );

    if (!hasRequiredPermission) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions to access this route'
      });
    }

    next();
  };
};

module.exports = {
  authorize,
  isAdmin,
  isStaffOrAdmin,
  isOwnerOrAdmin,
  checkResourceOwnership,
  hasPermission
};
