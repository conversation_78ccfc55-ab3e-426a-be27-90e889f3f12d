/* Admin User Management Styles */

.user-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.user-info strong {
    color: #333;
    font-weight: 600;
}

.user-info small {
    color: #666;
    font-size: 12px;
}

/* Role Badges */
.role-badge {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.role-user {
    background: #e3f2fd;
    color: #1976d2;
}

.role-staff {
    background: #fff3e0;
    color: #f57c00;
}

.role-admin {
    background: #fce4ec;
    color: #c2185b;
}

/* Status Badges */
.status-active {
    background: #e8f5e8;
    color: #2e7d32;
}

.status-inactive {
    background: #ffebee;
    color: #d32f2f;
}

/* User Modal */
.user-modal {
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.user-form {
    padding: 0;
}

.user-form .form-group {
    margin-bottom: 20px;
}

.user-form label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 600;
    font-size: 14px;
}

.user-form input,
.user-form select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.user-form input:focus,
.user-form select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.user-form input:disabled,
.user-form select:disabled {
    background: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-weight: 500;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkbox-label input[type="checkbox"]:disabled {
    cursor: not-allowed;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

/* User Statistics */
.user-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.stat-card .stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #007bff;
    margin-bottom: 5px;
}

.stat-card .stat-label {
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

/* User Activity */
.user-activity {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #f1f3f4;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: white;
}

.activity-login {
    background: #28a745;
}

.activity-order {
    background: #007bff;
}

.activity-review {
    background: #ffc107;
    color: #333;
}

.activity-info {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.activity-time {
    font-size: 12px;
    color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
    .user-stats {
        grid-template-columns: 1fr;
    }
    
    .user-form .form-actions {
        flex-direction: column;
    }
    
    .activity-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .activity-icon {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }
}
