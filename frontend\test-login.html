<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login - Na Food</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus {
            outline: none;
            border-color: #ff6b35;
        }
        .btn {
            width: 100%;
            padding: 15px;
            background: #ff6b35;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        .btn:hover {
            background: #e55a2b;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-accounts {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-accounts h3 {
            margin-top: 0;
            color: #0066cc;
        }
        .account {
            background: white;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #ccc;
        }
        .account strong {
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🍜 Na Food - Test Login</h1>
        
        <div class="test-accounts">
            <h3>📋 Test Accounts</h3>
            <div class="account">
                <strong>User Account:</strong><br>
                Email: <EMAIL><br>
                Password: 123456
            </div>
            <div class="account">
                <strong>Admin Account:</strong><br>
                Email: <EMAIL><br>
                Password: admin123
            </div>
        </div>
        
        <form id="test-login-form">
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" value="123456" required>
            </div>
            
            <button type="submit" class="btn">🔑 Test Login</button>
        </form>
        
        <div id="result" class="result"></div>
        
        <div style="margin-top: 30px; text-align: center;">
            <a href="/" style="color: #ff6b35; text-decoration: none; font-weight: 600;">← Back to Main Site</a>
        </div>
    </div>

    <script>
        document.getElementById('test-login-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.style.display = 'block';
                    resultDiv.innerHTML = `
                        <h4>✅ Login Successful!</h4>
                        <p><strong>User:</strong> ${data.user.name}</p>
                        <p><strong>Email:</strong> ${data.user.email}</p>
                        <p><strong>Role:</strong> ${data.user.role}</p>
                        <p><strong>Token:</strong> ${data.token.substring(0, 20)}...</p>
                        <button onclick="testAPI('${data.token}')" class="btn" style="margin-top: 10px;">Test API with Token</button>
                    `;
                    
                    // Store token for testing
                    localStorage.setItem('token', data.token);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.style.display = 'block';
                    resultDiv.innerHTML = `
                        <h4>❌ Login Failed</h4>
                        <p>${data.error || 'Unknown error'}</p>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = `
                    <h4>❌ Network Error</h4>
                    <p>${error.message}</p>
                `;
            }
        });
        
        async function testAPI(token) {
            try {
                const response = await fetch('/api/auth/me', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    alert('✅ API Test Successful!\n\nUser: ' + data.name + '\nEmail: ' + data.email);
                } else {
                    alert('❌ API Test Failed: ' + data.error);
                }
            } catch (error) {
                alert('❌ API Test Error: ' + error.message);
            }
        }
        
        // Quick fill buttons
        function fillUser() {
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('password').value = '123456';
        }
        
        function fillAdmin() {
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('password').value = 'admin123';
        }
    </script>
</body>
</html>
