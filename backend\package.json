{"name": "nafood-backend", "version": "1.0.0", "description": "Backend API for Na Food - Online Food Ordering System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "seed": "node createAdmin.js", "lint": "eslint . --ext .js", "lint:fix": "eslint . --ext .js --fix"}, "keywords": ["food", "ordering", "restaurant", "api"], "author": "Na Food Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "compression": "^1.7.4", "morgan": "^1.10.0", "pdfkit": "^0.13.0", "csv-writer": "^1.6.0", "moment": "^2.29.4"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}