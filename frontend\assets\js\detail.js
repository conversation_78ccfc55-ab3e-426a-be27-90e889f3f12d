// Product Detail Page Module
const ProductDetail = {
    currentProduct: null,
    selectedQuantity: 1,
    selectedVariant: null,

    init() {
        this.bindEvents();
        this.loadProductFromURL();
    },

    bindEvents() {
        // Quantity controls
        const quantityMinus = document.getElementById('quantity-minus');
        const quantityPlus = document.getElementById('quantity-plus');
        const quantityInput = document.getElementById('quantity-input');

        if (quantityMinus) {
            quantityMinus.addEventListener('click', () => {
                this.updateQuantity(-1);
            });
        }

        if (quantityPlus) {
            quantityPlus.addEventListener('click', () => {
                this.updateQuantity(1);
            });
        }

        if (quantityInput) {
            quantityInput.addEventListener('change', (e) => {
                this.setQuantity(parseInt(e.target.value) || 1);
            });
        }

        // Add to cart button
        const addToCartBtn = document.getElementById('add-to-cart-btn');
        if (addToCartBtn) {
            addToCartBtn.addEventListener('click', () => {
                this.addToCart();
            });
        }

        // Buy now button
        const buyNowBtn = document.getElementById('buy-now-btn');
        if (buyNowBtn) {
            buyNowBtn.addEventListener('click', () => {
                this.buyNow();
            });
        }

        // Image gallery
        this.bindImageGallery();

        // Variant selection
        this.bindVariantSelection();

        // Review form
        this.bindReviewForm();
    },

    loadProductFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        const productId = urlParams.get('id');
        
        if (productId) {
            this.loadProduct(productId);
        } else {
            this.showError('Không tìm thấy sản phẩm');
        }
    },

    async loadProduct(productId) {
        try {
            Loading.show();

            const response = await API.get(`/products/${productId}`);
            this.currentProduct = response.data;

            this.renderProduct();
            this.loadReviews();

        } catch (error) {
            console.error('Load product error:', error);
            this.showError('Không thể tải thông tin sản phẩm');
        } finally {
            Loading.hide();
        }
    },

    renderProduct() {
        if (!this.currentProduct) return;

        const product = this.currentProduct;

        // Update page title
        document.title = `${product.name} - Na Food`;

        // Product images
        this.renderImageGallery(product.images);

        // Product info
        this.renderProductInfo(product);

        // Product description
        this.renderProductDescription(product);

        // Related products
        this.loadRelatedProducts(product.category);
    },

    renderImageGallery(images) {
        const mainImage = document.getElementById('main-product-image');
        const thumbnails = document.getElementById('image-thumbnails');

        if (!images || images.length === 0) {
            if (mainImage) {
                mainImage.src = '/images/placeholder.jpg';
                mainImage.alt = 'Product image';
            }
            return;
        }

        // Main image
        if (mainImage) {
            mainImage.src = images[0];
            mainImage.alt = this.currentProduct.name;
        }

        // Thumbnails
        if (thumbnails) {
            thumbnails.innerHTML = images.map((image, index) => `
                <img src="${image}" 
                     alt="Product image ${index + 1}"
                     class="thumbnail ${index === 0 ? 'active' : ''}"
                     onclick="ProductDetail.selectImage(${index})">
            `).join('');
        }
    },

    selectImage(index) {
        const mainImage = document.getElementById('main-product-image');
        const thumbnails = document.querySelectorAll('.thumbnail');

        if (mainImage && this.currentProduct.images[index]) {
            mainImage.src = this.currentProduct.images[index];
        }

        thumbnails.forEach((thumb, i) => {
            thumb.classList.toggle('active', i === index);
        });
    },

    renderProductInfo(product) {
        // Product name
        const nameElement = document.getElementById('product-name');
        if (nameElement) {
            nameElement.textContent = product.name;
        }

        // Product price
        const priceElement = document.getElementById('product-price');
        if (priceElement) {
            if (product.salePrice && product.salePrice < product.price) {
                priceElement.innerHTML = `
                    <span class="sale-price">${Utils.formatCurrency(product.salePrice)}</span>
                    <span class="original-price">${Utils.formatCurrency(product.price)}</span>
                    <span class="discount-badge">-${Math.round((1 - product.salePrice / product.price) * 100)}%</span>
                `;
            } else {
                priceElement.innerHTML = `<span class="current-price">${Utils.formatCurrency(product.price)}</span>`;
            }
        }

        // Product rating
        const ratingElement = document.getElementById('product-rating');
        if (ratingElement) {
            ratingElement.innerHTML = `
                <div class="stars">${this.renderStars(product.averageRating || 0)}</div>
                <span class="rating-text">${product.averageRating || 0}/5 (${product.reviewCount || 0} đánh giá)</span>
            `;
        }

        // Product category
        const categoryElement = document.getElementById('product-category');
        if (categoryElement) {
            categoryElement.textContent = product.category;
        }

        // Product availability
        const availabilityElement = document.getElementById('product-availability');
        if (availabilityElement) {
            if (product.isAvailable) {
                availabilityElement.innerHTML = '<span class="in-stock">✓ Còn hàng</span>';
            } else {
                availabilityElement.innerHTML = '<span class="out-of-stock">✗ Hết hàng</span>';
            }
        }
    },

    renderProductDescription(product) {
        const descriptionElement = document.getElementById('product-description');
        if (descriptionElement) {
            descriptionElement.innerHTML = product.description || 'Chưa có mô tả cho sản phẩm này.';
        }

        // Ingredients
        const ingredientsElement = document.getElementById('product-ingredients');
        if (ingredientsElement && product.ingredients) {
            ingredientsElement.innerHTML = product.ingredients.join(', ');
        }

        // Nutrition info
        const nutritionElement = document.getElementById('nutrition-info');
        if (nutritionElement && product.nutrition) {
            nutritionElement.innerHTML = `
                <div class="nutrition-item">
                    <span>Calories:</span>
                    <span>${product.nutrition.calories || 'N/A'}</span>
                </div>
                <div class="nutrition-item">
                    <span>Protein:</span>
                    <span>${product.nutrition.protein || 'N/A'}g</span>
                </div>
                <div class="nutrition-item">
                    <span>Carbs:</span>
                    <span>${product.nutrition.carbs || 'N/A'}g</span>
                </div>
                <div class="nutrition-item">
                    <span>Fat:</span>
                    <span>${product.nutrition.fat || 'N/A'}g</span>
                </div>
            `;
        }
    },

    updateQuantity(change) {
        this.selectedQuantity = Math.max(1, this.selectedQuantity + change);
        this.updateQuantityDisplay();
    },

    setQuantity(quantity) {
        this.selectedQuantity = Math.max(1, quantity);
        this.updateQuantityDisplay();
    },

    updateQuantityDisplay() {
        const quantityInput = document.getElementById('quantity-input');
        if (quantityInput) {
            quantityInput.value = this.selectedQuantity;
        }

        // Update total price if variant is selected
        this.updateTotalPrice();
    },

    updateTotalPrice() {
        const totalPriceElement = document.getElementById('total-price');
        if (totalPriceElement && this.currentProduct) {
            const price = this.currentProduct.salePrice || this.currentProduct.price;
            const total = price * this.selectedQuantity;
            totalPriceElement.textContent = Utils.formatCurrency(total);
        }
    },

    bindImageGallery() {
        // Image zoom functionality
        const mainImage = document.getElementById('main-product-image');
        if (mainImage) {
            mainImage.addEventListener('click', () => {
                this.openImageModal(mainImage.src);
            });
        }
    },

    openImageModal(imageSrc) {
        const modalContent = `
            <div class="image-modal">
                <div class="modal-header">
                    <h3>Hình ảnh sản phẩm</h3>
                    <button class="modal-close" onclick="Modal.hide()">&times;</button>
                </div>
                <div class="modal-body">
                    <img src="${imageSrc}" alt="Product image" class="modal-image">
                </div>
            </div>
        `;
        Modal.show(modalContent);
    },

    bindVariantSelection() {
        const variantOptions = document.querySelectorAll('.variant-option');
        variantOptions.forEach(option => {
            option.addEventListener('click', () => {
                this.selectVariant(option.dataset.variant);
            });
        });
    },

    selectVariant(variantId) {
        // Update selected variant
        this.selectedVariant = variantId;

        // Update UI
        document.querySelectorAll('.variant-option').forEach(option => {
            option.classList.toggle('selected', option.dataset.variant === variantId);
        });

        // Update price if variant has different price
        this.updateTotalPrice();
    },

    async addToCart() {
        if (!this.currentProduct) return;

        try {
            const cartItem = {
                productId: this.currentProduct._id,
                name: this.currentProduct.name,
                price: this.currentProduct.salePrice || this.currentProduct.price,
                image: this.currentProduct.images?.[0] || '/images/placeholder.jpg',
                quantity: this.selectedQuantity,
                variant: this.selectedVariant
            };

            Cart.addItem(cartItem);
            Toast.success(`Đã thêm ${this.currentProduct.name} vào giỏ hàng!`);

            // Update cart UI
            Header.updateCartCount();

        } catch (error) {
            console.error('Add to cart error:', error);
            Toast.error('Có lỗi xảy ra khi thêm vào giỏ hàng');
        }
    },

    async buyNow() {
        await this.addToCart();
        // Redirect to checkout
        window.location.href = '/checkout.html';
    },

    renderStars(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

        let starsHTML = '';
        
        for (let i = 0; i < fullStars; i++) {
            starsHTML += '<i class="fas fa-star"></i>';
        }
        
        if (hasHalfStar) {
            starsHTML += '<i class="fas fa-star-half-alt"></i>';
        }
        
        for (let i = 0; i < emptyStars; i++) {
            starsHTML += '<i class="far fa-star"></i>';
        }

        return starsHTML;
    },

    async loadReviews() {
        try {
            const response = await API.get(`/products/${this.currentProduct._id}/reviews`);
            this.renderReviews(response.data);
        } catch (error) {
            console.error('Load reviews error:', error);
        }
    },

    renderReviews(reviews) {
        const reviewsContainer = document.getElementById('reviews-list');
        if (!reviewsContainer) return;

        if (reviews.length === 0) {
            reviewsContainer.innerHTML = `
                <div class="empty-reviews">
                    <i class="fas fa-star"></i>
                    <h4>Chưa có đánh giá nào</h4>
                    <p>Hãy là người đầu tiên đánh giá sản phẩm này!</p>
                </div>
            `;
            return;
        }

        reviewsContainer.innerHTML = reviews.map(review => `
            <div class="review-item">
                <div class="review-header">
                    <div class="reviewer-info">
                        <div class="reviewer-avatar">${review.user.name.charAt(0).toUpperCase()}</div>
                        <div class="reviewer-details">
                            <h5>${review.user.name}</h5>
                            <div class="review-rating">${this.renderStars(review.rating)}</div>
                            <div class="review-date">${Utils.formatDate(review.createdAt)}</div>
                        </div>
                    </div>
                </div>
                <div class="review-content">
                    ${review.title ? `<div class="review-title">${review.title}</div>` : ''}
                    <div class="review-text">${review.comment}</div>
                </div>
                <div class="review-footer">
                    <div class="helpful-count">
                        <button class="btn-helpful" onclick="ProductDetail.markHelpful('${review._id}')">
                            <i class="fas fa-thumbs-up"></i> Hữu ích (${review.helpfulCount || 0})
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    },

    bindReviewForm() {
        const writeReviewBtn = document.getElementById('write-review-btn');
        if (writeReviewBtn) {
            writeReviewBtn.addEventListener('click', () => {
                this.showReviewForm();
            });
        }
    },

    showReviewForm() {
        // Check if user is logged in
        const token = Storage.get('token');
        if (!token) {
            Toast.error('Vui lòng đăng nhập để viết đánh giá');
            return;
        }

        const modalContent = `
            <div class="review-modal">
                <div class="modal-header">
                    <h3>Viết đánh giá</h3>
                    <button class="modal-close" onclick="Modal.hide()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="review-form" class="review-form">
                        <div class="form-group">
                            <label>Đánh giá *</label>
                            <div class="rating-input" id="rating-input">
                                ${[1,2,3,4,5].map(i => `<i class="star-input far fa-star" data-rating="${i}"></i>`).join('')}
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>Tiêu đề</label>
                            <input type="text" name="title" placeholder="Tóm tắt đánh giá của bạn">
                        </div>
                        
                        <div class="form-group">
                            <label>Nội dung *</label>
                            <textarea name="comment" rows="4" placeholder="Chia sẻ trải nghiệm của bạn về sản phẩm này..." required></textarea>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="form-btn btn-submit">Gửi đánh giá</button>
                            <button type="button" class="form-btn btn-cancel" onclick="Modal.hide()">Hủy</button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        Modal.show(modalContent);

        // Bind rating stars
        this.bindRatingStars();

        // Bind form submit
        document.getElementById('review-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitReview(e.target);
        });
    },

    bindRatingStars() {
        const stars = document.querySelectorAll('.star-input');
        let selectedRating = 0;

        stars.forEach((star, index) => {
            star.addEventListener('click', () => {
                selectedRating = index + 1;
                this.updateStarDisplay(selectedRating);
            });

            star.addEventListener('mouseover', () => {
                this.updateStarDisplay(index + 1);
            });
        });

        document.getElementById('rating-input').addEventListener('mouseleave', () => {
            this.updateStarDisplay(selectedRating);
        });

        // Store rating in the object
        document.getElementById('rating-input').dataset.selectedRating = selectedRating;
    },

    updateStarDisplay(rating) {
        const stars = document.querySelectorAll('.star-input');
        stars.forEach((star, index) => {
            if (index < rating) {
                star.classList.remove('far');
                star.classList.add('fas');
            } else {
                star.classList.remove('fas');
                star.classList.add('far');
            }
        });
    },

    async submitReview(form) {
        try {
            const formData = new FormData(form);
            const selectedRating = parseInt(document.getElementById('rating-input').dataset.selectedRating) || 0;

            const reviewData = {
                productId: this.currentProduct._id,
                rating: selectedRating,
                title: formData.get('title'),
                comment: formData.get('comment')
            };

            if (!reviewData.rating) {
                Toast.error('Vui lòng chọn số sao đánh giá');
                return;
            }

            Loading.show();

            await API.post('/reviews', reviewData);
            
            Modal.hide();
            Toast.success('Cảm ơn bạn đã đánh giá sản phẩm!');
            
            // Reload reviews
            this.loadReviews();

        } catch (error) {
            console.error('Submit review error:', error);
            Toast.error('Có lỗi xảy ra khi gửi đánh giá');
        } finally {
            Loading.hide();
        }
    },

    async markHelpful(reviewId) {
        try {
            await API.post(`/reviews/${reviewId}/helpful`);
            Toast.success('Cảm ơn phản hồi của bạn!');
            this.loadReviews();
        } catch (error) {
            console.error('Mark helpful error:', error);
            Toast.error('Có lỗi xảy ra');
        }
    },

    async loadRelatedProducts(category) {
        try {
            const response = await API.get(`/products?category=${category}&limit=4`);
            this.renderRelatedProducts(response.data.filter(p => p._id !== this.currentProduct._id));
        } catch (error) {
            console.error('Load related products error:', error);
        }
    },

    renderRelatedProducts(products) {
        const container = document.getElementById('related-products');
        if (!container || products.length === 0) return;

        container.innerHTML = `
            <h3>Sản phẩm liên quan</h3>
            <div class="related-products-grid">
                ${products.map(product => `
                    <div class="product-card" onclick="window.location.href='/detail.html?id=${product._id}'">
                        <img src="${product.images?.[0] || '/images/placeholder.jpg'}" alt="${product.name}">
                        <h4>${product.name}</h4>
                        <div class="price">${Utils.formatCurrency(product.salePrice || product.price)}</div>
                    </div>
                `).join('')}
            </div>
        `;
    },

    showError(message) {
        const container = document.getElementById('product-detail-container');
        if (container) {
            container.innerHTML = `
                <div class="error-state">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>Có lỗi xảy ra</h3>
                    <p>${message}</p>
                    <button onclick="window.location.href='/'" class="btn btn-primary">Về trang chủ</button>
                </div>
            `;
        }
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    ProductDetail.init();
});

// Export for global use
window.ProductDetail = ProductDetail;
