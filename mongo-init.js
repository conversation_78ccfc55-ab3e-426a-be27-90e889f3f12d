// MongoDB initialization script for Na Food
db = db.getSiblingDB('nafood');

// Create application user
db.createUser({
  user: 'nafood_user',
  pwd: 'nafood_password',
  roles: [
    {
      role: 'readWrite',
      db: 'nafood'
    }
  ]
});

// Create indexes for better performance
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ role: 1 });
db.users.createIndex({ isActive: 1 });

db.products.createIndex({ name: "text", description: "text", tags: "text" });
db.products.createIndex({ category: 1, isAvailable: 1 });
db.products.createIndex({ isFeatured: 1, isAvailable: 1 });
db.products.createIndex({ "rating.average": -1 });
db.products.createIndex({ soldCount: -1 });
db.products.createIndex({ price: 1 });
db.products.createIndex({ createdAt: -1 });

db.orders.createIndex({ user: 1, createdAt: -1 });
db.orders.createIndex({ status: 1, createdAt: -1 });
db.orders.createIndex({ orderNumber: 1 }, { unique: true });
db.orders.createIndex({ paymentStatus: 1 });
db.orders.createIndex({ createdAt: -1 });

db.reviews.createIndex({ product: 1, isApproved: 1, isHidden: 1 });
db.reviews.createIndex({ user: 1, createdAt: -1 });
db.reviews.createIndex({ rating: -1 });
db.reviews.createIndex({ helpfulCount: -1 });
db.reviews.createIndex({ createdAt: -1 });
db.reviews.createIndex({ user: 1, product: 1, order: 1 }, { unique: true });

db.banners.createIndex({ isActive: 1, position: 1, displayOrder: 1 });
db.banners.createIndex({ type: 1, isActive: 1 });
db.banners.createIndex({ startDate: 1, endDate: 1 });
db.banners.createIndex({ displayOrder: 1 });

// Insert sample admin user
db.users.insertOne({
  name: "Admin User",
  email: "<EMAIL>",
  password: "$2a$10$8K1p/a0dRt.uMPWgWvKvNOyGDS.JQ/CTBRjHHPPvEw5/sC.daK4Vi", // password: admin123
  role: "admin",
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date()
});

// Insert sample categories data
db.products.insertMany([
  {
    name: "Phở Bò Tái",
    description: "Phở bò tái truyền thống với nước dùng đậm đà, thịt bò tái mềm ngon",
    price: 65000,
    category: "traditional",
    images: [
      {
        url: "/images/pho-bo-tai.jpg",
        alt: "Phở Bò Tái",
        isPrimary: true
      }
    ],
    ingredients: ["Bánh phở", "Thịt bò tái", "Hành lá", "Ngò gai", "Giá đỗ"],
    isAvailable: true,
    isFeatured: true,
    preparationTime: 15,
    spicyLevel: 1,
    rating: { average: 4.5, count: 25 },
    soldCount: 150,
    viewCount: 500,
    createdBy: ObjectId(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: "Bánh Mì Thịt Nướng",
    description: "Bánh mì giòn với thịt nướng thơm lừng, rau sống tươi ngon",
    price: 25000,
    category: "fast-food",
    images: [
      {
        url: "/images/banh-mi-thit-nuong.jpg",
        alt: "Bánh Mì Thịt Nướng",
        isPrimary: true
      }
    ],
    ingredients: ["Bánh mì", "Thịt nướng", "Pate", "Rau sống", "Đồ chua"],
    isAvailable: true,
    isFeatured: false,
    preparationTime: 10,
    spicyLevel: 2,
    rating: { average: 4.2, count: 18 },
    soldCount: 89,
    viewCount: 320,
    createdBy: ObjectId(),
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

// Insert sample banners
db.banners.insertMany([
  {
    title: "Chào mừng đến với Na Food",
    subtitle: "Thưởng thức ẩm thực Việt Nam chính gốc",
    description: "Đặt hàng ngay để nhận ưu đãi 20% cho đơn hàng đầu tiên",
    image: {
      url: "/images/banner-welcome.jpg",
      alt: "Welcome Banner"
    },
    link: {
      url: "/products",
      text: "Đặt hàng ngay",
      target: "_self"
    },
    type: "hero",
    position: "main",
    displayOrder: 1,
    isActive: true,
    targetAudience: "all",
    backgroundColor: "#ff6b35",
    textColor: "#ffffff",
    buttonStyle: {
      backgroundColor: "#ffffff",
      textColor: "#ff6b35",
      borderRadius: 25
    },
    createdBy: ObjectId(),
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

print("Database initialized successfully!");
