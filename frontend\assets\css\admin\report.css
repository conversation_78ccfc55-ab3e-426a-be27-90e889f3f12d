/* Admin Report Styles */

.report-container {
    padding: 0;
}

/* Report Controls */
.report-controls {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
    min-width: 150px;
}

.control-group label {
    font-size: 12px;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
}

.control-group input,
.control-group select {
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.control-group input:focus,
.control-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Report Summary */
.report-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.summary-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.summary-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.summary-card:nth-child(1) .summary-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.summary-card:nth-child(2) .summary-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.summary-card:nth-child(3) .summary-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.summary-card:nth-child(4) .summary-icon {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.summary-info h3 {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.summary-info p {
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

/* Report Charts */
.report-charts {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-bottom: 40px;
}

.chart-section {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.chart-section h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.chart-section canvas {
    max-height: 300px;
}

/* Report Section */
.report-section {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 30px;
}

.report-section h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

/* Top Products Table */
.top-products-table {
    overflow-x: auto;
}

.top-products-table .data-table {
    width: 100%;
    border-collapse: collapse;
}

.top-products-table .data-table th,
.top-products-table .data-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.top-products-table .data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.top-products-table .data-table tr:hover {
    background: #f8f9fa;
}

.product-rank {
    display: flex;
    align-items: center;
    gap: 10px;
}

.rank {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 12px;
}

.rank:nth-child(1) {
    background: #ffd700;
    color: #333;
}

.name {
    font-weight: 600;
    color: #333;
}

/* Customer Stats */
.customer-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-label {
    color: #666;
    font-weight: 500;
}

.stat-value {
    font-weight: 700;
    color: #333;
    font-size: 18px;
}

/* Report Actions */
.report-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 40px;
    padding-top: 30px;
    border-top: 2px solid #e9ecef;
}

.report-actions .btn {
    padding: 15px 30px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
}

.btn-primary {
    background: #dc3545;
    color: white;
}

.btn-primary:hover {
    background: #c82333;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #28a745;
    color: white;
}

.btn-secondary:hover {
    background: #218838;
    transform: translateY(-2px);
}

/* Date Range Picker */
.date-range-picker {
    display: flex;
    gap: 10px;
    align-items: center;
}

.date-range-picker input {
    padding: 8px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
}

.date-range-picker span {
    color: #666;
    font-weight: 500;
}

/* Report Filters */
.report-filters {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-size: 12px;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
}

/* Performance Metrics */
.performance-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.metric-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.metric-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.metric-value.positive {
    color: #28a745;
}

.metric-value.negative {
    color: #dc3545;
}

.metric-value.neutral {
    color: #007bff;
}

.metric-label {
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

.metric-change {
    font-size: 12px;
    margin-top: 5px;
}

.metric-change.up {
    color: #28a745;
}

.metric-change.down {
    color: #dc3545;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .report-charts {
        grid-template-columns: 1fr;
    }
    
    .report-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .control-group {
        min-width: auto;
    }
}

@media (max-width: 768px) {
    .report-summary {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .customer-stats {
        grid-template-columns: 1fr;
    }
    
    .report-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .performance-metrics {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .filter-row {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .report-summary {
        grid-template-columns: 1fr;
    }
    
    .summary-card {
        padding: 20px;
    }
    
    .summary-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .summary-info h3 {
        font-size: 24px;
    }
    
    .performance-metrics {
        grid-template-columns: 1fr;
    }
    
    .chart-section {
        padding: 20px;
    }
}
