/* Authentication Styles */

.auth-modal {
    width: 100%;
    max-width: 450px;
    background: white;
    border-radius: 15px;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.modal-header h2 {
    color: #333;
    font-size: 24px;
    font-weight: 600;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: #e9ecef;
    color: #333;
}

.modal-body {
    padding: 30px;
}

/* Auth Tabs */
.auth-tabs {
    display: flex;
    margin-bottom: 30px;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 5px;
}

.auth-tab {
    flex: 1;
    padding: 12px 20px;
    background: transparent;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #666;
}

.auth-tab.active {
    background: white;
    color: #ff6b35;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.auth-tab:hover {
    color: #ff6b35;
}

/* Auth Forms */
.auth-form {
    display: block;
}

.auth-form.active {
    display: block;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
    font-size: 14px;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #ff6b35;
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.form-group.error input,
.form-group.error textarea,
.form-group.error select {
    border-color: #dc3545;
}

.form-error {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    display: none;
}

.form-group.error .form-error {
    display: block;
}

.form-group input[type="checkbox"] {
    width: auto;
    margin-right: 10px;
}

.checkbox-group {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.checkbox-group label {
    margin-bottom: 0;
    cursor: pointer;
    font-size: 14px;
}

.checkbox-group a {
    color: #ff6b35;
    text-decoration: none;
}

.checkbox-group a:hover {
    text-decoration: underline;
}

/* Buttons */
.btn,
.auth-btn {
    padding: 15px 25px;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    text-decoration: none;
    width: 100%;
}

.auth-btn {
    background: #ff6b35;
    color: white;
    margin-top: 10px;
}

.auth-btn:hover {
    background: #e55a2b;
    transform: translateY(-2px);
}

/* Social Login Buttons */
.social-login {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin: 20px 0;
}

.social-btn {
    padding: 12px 20px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: white;
    color: #333;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    text-decoration: none;
}

.social-btn:hover {
    border-color: #ccc;
    background: #f8f9fa;
}

.google-btn:hover {
    border-color: #db4437;
    color: #db4437;
}

.facebook-btn:hover {
    border-color: #3b5998;
    color: #3b5998;
}

/* Auth Divider */
.auth-divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e9ecef;
}

.auth-divider span {
    background: white;
    padding: 0 15px;
    color: #666;
    font-size: 14px;
}

/* Auth Switch */
.auth-switch {
    text-align: center;
    margin-top: 20px;
    color: #666;
    font-size: 14px;
}

.auth-switch a {
    color: #ff6b35;
    text-decoration: none;
    font-weight: 600;
}

.auth-switch a:hover {
    text-decoration: underline;
}

.btn-primary {
    background: #ff6b35;
    color: white;
}

.btn-primary:hover {
    background: #e55a2b;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: #ff6b35;
    border: 2px solid #ff6b35;
}

.btn-outline:hover {
    background: #ff6b35;
    color: white;
}

.btn-full {
    width: 100%;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-loading {
    position: relative;
    color: transparent;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
}

.form-actions .btn {
    flex: 1;
}

/* Social Login */
.social-login {
    margin-top: 25px;
    padding-top: 25px;
    border-top: 1px solid #e9ecef;
}

.social-login-title {
    text-align: center;
    color: #666;
    margin-bottom: 20px;
    font-size: 14px;
}

.social-buttons {
    display: flex;
    gap: 15px;
}

.btn-social {
    flex: 1;
    padding: 12px;
    border: 2px solid #e9ecef;
    background: white;
    color: #333;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.btn-social:hover {
    border-color: #ff6b35;
    color: #ff6b35;
}

.btn-social.google {
    color: #db4437;
}

.btn-social.google:hover {
    border-color: #db4437;
    background: #db4437;
    color: white;
}

.btn-social.facebook {
    color: #3b5998;
}

.btn-social.facebook:hover {
    border-color: #3b5998;
    background: #3b5998;
    color: white;
}

/* Forgot Password */
.forgot-password {
    text-align: center;
    margin-top: 20px;
}

.forgot-password a {
    color: #ff6b35;
    text-decoration: none;
    font-size: 14px;
}

.forgot-password a:hover {
    text-decoration: underline;
}

/* Profile Modal */
.profile-modal {
    width: 100%;
    max-width: 500px;
}

.profile-modal .modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

/* Address Fields */
.address-fields {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.address-fields .form-group {
    margin-bottom: 15px;
}

/* Password Strength */
.password-strength {
    margin-top: 8px;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
}

.password-strength-bar {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.password-strength.weak .password-strength-bar {
    width: 33%;
    background: #dc3545;
}

.password-strength.medium .password-strength-bar {
    width: 66%;
    background: #ffc107;
}

.password-strength.strong .password-strength-bar {
    width: 100%;
    background: #28a745;
}

.password-requirements {
    margin-top: 10px;
    font-size: 12px;
    color: #666;
}

.password-requirements ul {
    margin: 5px 0;
    padding-left: 20px;
}

.password-requirements li {
    margin-bottom: 2px;
}

.password-requirements li.valid {
    color: #28a745;
}

.password-requirements li.invalid {
    color: #dc3545;
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-modal {
        max-width: 90vw;
        margin: 20px;
    }
    
    .modal-header,
    .modal-body {
        padding: 20px;
    }
    
    .address-fields {
        grid-template-columns: 1fr;
    }
    
    .social-buttons {
        flex-direction: column;
    }
    
    .form-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .auth-modal {
        max-width: 95vw;
        margin: 10px;
    }
    
    .modal-header,
    .modal-body {
        padding: 15px;
    }
    
    .modal-header h2 {
        font-size: 20px;
    }
    
    .form-group input,
    .form-group textarea,
    .form-group select {
        padding: 12px;
        font-size: 14px;
    }
    
    .btn {
        padding: 12px 20px;
        font-size: 14px;
    }
}
