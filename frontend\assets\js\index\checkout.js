// Checkout Page Module
const Checkout = {
    cartItems: [],
    deliveryInfo: {},
    paymentMethod: 'cod',
    promoCode: null,
    deliveryFee: 30000,

    init() {
        this.loadCartItems();
        this.bindEvents();
        this.renderOrderSummary();
    },

    bindEvents() {
        // Form validation
        const checkoutForm = document.getElementById('checkout-form');
        if (checkoutForm) {
            checkoutForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.processOrder();
            });
        }

        // Payment method selection
        const paymentMethods = document.querySelectorAll('input[name="payment-method"]');
        paymentMethods.forEach(method => {
            method.addEventListener('change', (e) => {
                this.selectPaymentMethod(e.target.value);
            });
        });

        // Promo code
        const promoBtn = document.getElementById('apply-promo-btn');
        if (promoBtn) {
            promoBtn.addEventListener('click', () => {
                this.applyPromoCode();
            });
        }

        // Form inputs validation
        this.bindFormValidation();
    },

    loadCartItems() {
        this.cartItems = Cart.getItems();
        
        if (this.cartItems.length === 0) {
            this.showEmptyCart();
            return;
        }

        this.renderCartItems();
    },

    renderCartItems() {
        const container = document.getElementById('checkout-items');
        if (!container) return;

        container.innerHTML = this.cartItems.map(item => `
            <div class="order-item">
                <img src="${item.image}" alt="${item.name}" class="item-image">
                <div class="item-info">
                    <div class="item-name">${item.name}</div>
                    <div class="item-details">
                        ${item.variant ? `Phân loại: ${item.variant}` : ''}
                        ${item.specialInstructions ? `<br>Ghi chú: ${item.specialInstructions}` : ''}
                    </div>
                    <div class="item-quantity">Số lượng: ${item.quantity}</div>
                </div>
                <div class="item-price">${Utils.formatCurrency(item.price * item.quantity)}</div>
            </div>
        `).join('');
    },

    renderOrderSummary() {
        const subtotal = this.calculateSubtotal();
        const discount = this.calculateDiscount();
        const total = subtotal + this.deliveryFee - discount;

        const summaryContainer = document.getElementById('order-totals');
        if (summaryContainer) {
            summaryContainer.innerHTML = `
                <div class="total-row subtotal">
                    <span>Tạm tính:</span>
                    <span>${Utils.formatCurrency(subtotal)}</span>
                </div>
                <div class="total-row delivery">
                    <span>Phí giao hàng:</span>
                    <span>${Utils.formatCurrency(this.deliveryFee)}</span>
                </div>
                ${discount > 0 ? `
                    <div class="total-row discount">
                        <span>Giảm giá:</span>
                        <span>-${Utils.formatCurrency(discount)}</span>
                    </div>
                ` : ''}
                <div class="total-row final">
                    <span>Tổng cộng:</span>
                    <span>${Utils.formatCurrency(total)}</span>
                </div>
            `;
        }
    },

    calculateSubtotal() {
        return this.cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
    },

    calculateDiscount() {
        if (!this.promoCode) return 0;
        
        const subtotal = this.calculateSubtotal();
        
        // Mock promo codes
        const promoCodes = {
            'WELCOME10': { type: 'percentage', value: 10, minOrder: 100000 },
            'SAVE50K': { type: 'fixed', value: 50000, minOrder: 200000 },
            'FREESHIP': { type: 'shipping', value: this.deliveryFee, minOrder: 150000 }
        };

        const promo = promoCodes[this.promoCode];
        if (!promo || subtotal < promo.minOrder) return 0;

        switch (promo.type) {
            case 'percentage':
                return Math.round(subtotal * promo.value / 100);
            case 'fixed':
                return promo.value;
            case 'shipping':
                this.deliveryFee = 0;
                return 0;
            default:
                return 0;
        }
    },

    selectPaymentMethod(method) {
        this.paymentMethod = method;
        
        // Update UI
        document.querySelectorAll('.payment-method').forEach(el => {
            el.classList.remove('selected');
        });
        
        const selectedMethod = document.querySelector(`input[value="${method}"]`)?.closest('.payment-method');
        if (selectedMethod) {
            selectedMethod.classList.add('selected');
        }

        // Show/hide payment forms
        this.togglePaymentForms(method);
    },

    togglePaymentForms(method) {
        const forms = document.querySelectorAll('.payment-form');
        forms.forEach(form => {
            form.classList.remove('active');
        });

        const activeForm = document.getElementById(`${method}-form`);
        if (activeForm) {
            activeForm.classList.add('active');
        }
    },

    applyPromoCode() {
        const promoInput = document.getElementById('promo-code-input');
        const promoCode = promoInput?.value?.trim().toUpperCase();

        if (!promoCode) {
            Toast.error('Vui lòng nhập mã giảm giá');
            return;
        }

        // Mock validation
        const validCodes = ['WELCOME10', 'SAVE50K', 'FREESHIP'];
        
        if (validCodes.includes(promoCode)) {
            this.promoCode = promoCode;
            this.renderOrderSummary();
            
            // Show applied state
            const appliedDiv = document.getElementById('promo-applied');
            if (appliedDiv) {
                appliedDiv.classList.add('active');
                appliedDiv.innerHTML = `
                    <i class="fas fa-check"></i>
                    Đã áp dụng mã "${promoCode}"
                    <button onclick="Checkout.removePromoCode()" class="remove-promo">×</button>
                `;
            }
            
            if (promoInput) promoInput.disabled = true;
            Toast.success('Áp dụng mã giảm giá thành công!');
        } else {
            Toast.error('Mã giảm giá không hợp lệ');
        }
    },

    removePromoCode() {
        this.promoCode = null;
        this.deliveryFee = 30000; // Reset delivery fee
        this.renderOrderSummary();
        
        const promoInput = document.getElementById('promo-code-input');
        const appliedDiv = document.getElementById('promo-applied');
        
        if (promoInput) {
            promoInput.value = '';
            promoInput.disabled = false;
        }
        
        if (appliedDiv) {
            appliedDiv.classList.remove('active');
        }
        
        Toast.info('Đã hủy mã giảm giá');
    },

    bindFormValidation() {
        const inputs = document.querySelectorAll('#checkout-form input, #checkout-form select');
        
        inputs.forEach(input => {
            input.addEventListener('blur', () => {
                this.validateField(input);
            });
            
            input.addEventListener('input', () => {
                this.clearFieldError(input);
            });
        });
    },

    validateField(field) {
        const value = field.value.trim();
        const fieldName = field.name;
        let isValid = true;
        let errorMessage = '';

        // Required field validation
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            errorMessage = 'Trường này là bắt buộc';
        }

        // Specific field validations
        switch (fieldName) {
            case 'email':
                if (value && !Utils.isValidEmail(value)) {
                    isValid = false;
                    errorMessage = 'Email không hợp lệ';
                }
                break;
            case 'phone':
                if (value && !Utils.isValidPhone(value)) {
                    isValid = false;
                    errorMessage = 'Số điện thoại không hợp lệ';
                }
                break;
        }

        this.showFieldError(field, isValid, errorMessage);
        return isValid;
    },

    showFieldError(field, isValid, message) {
        const formGroup = field.closest('.form-group');
        const errorElement = formGroup?.querySelector('.form-error');

        if (formGroup) {
            formGroup.classList.toggle('error', !isValid);
        }

        if (errorElement) {
            errorElement.textContent = message;
        }
    },

    clearFieldError(field) {
        const formGroup = field.closest('.form-group');
        if (formGroup) {
            formGroup.classList.remove('error');
        }
    },

    validateForm() {
        const form = document.getElementById('checkout-form');
        if (!form) return false;
        
        const inputs = form.querySelectorAll('input[required], select[required]');
        let isValid = true;

        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });

        return isValid;
    },

    async processOrder() {
        if (!this.validateForm()) {
            Toast.error('Vui lòng kiểm tra lại thông tin');
            return;
        }

        try {
            Loading.show();

            // Collect form data
            const formData = new FormData(document.getElementById('checkout-form'));
            
            const orderData = {
                items: this.cartItems.map(item => ({
                    productId: item.productId,
                    name: item.name,
                    price: item.price,
                    quantity: item.quantity,
                    image: item.image,
                    variant: item.variant,
                    specialInstructions: item.specialInstructions
                })),
                deliveryInfo: {
                    name: formData.get('name'),
                    phone: formData.get('phone'),
                    email: formData.get('email'),
                    address: formData.get('address'),
                    ward: formData.get('ward'),
                    district: formData.get('district'),
                    city: formData.get('city'),
                    notes: formData.get('notes')
                },
                paymentMethod: this.paymentMethod,
                subtotal: this.calculateSubtotal(),
                deliveryFee: this.deliveryFee,
                discount: {
                    code: this.promoCode,
                    amount: this.calculateDiscount()
                },
                total: this.calculateSubtotal() + this.deliveryFee - this.calculateDiscount()
            };

            // Submit order
            const response = await API.post('/orders', orderData);
            
            // Clear cart
            Cart.clear();
            
            // Show success and navigate to payment view
            Toast.success('Đặt hàng thành công!');
            App.showView('payment', { orderId: response.data._id, status: 'success' });

        } catch (error) {
            console.error('Process order error:', error);
            Toast.error('Có lỗi xảy ra khi đặt hàng. Vui lòng thử lại.');
        } finally {
            Loading.hide();
        }
    },

    showEmptyCart() {
        const container = document.getElementById('checkout-container');
        if (container) {
            container.innerHTML = `
                <div class="empty-cart">
                    <i class="fas fa-shopping-cart"></i>
                    <h3>Giỏ hàng trống</h3>
                    <p>Bạn chưa có sản phẩm nào trong giỏ hàng</p>
                    <button onclick="App.showView('home')" class="btn btn-primary">Tiếp tục mua sắm</button>
                </div>
            `;
        }
    }
};

// Export for global use
window.Checkout = Checkout;
