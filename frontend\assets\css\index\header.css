/* Header Styles for Na Food */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Loading Spinner */
.loading-spinner {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #ff6b35;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Header */
.header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.navbar {
    padding: 0 20px;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    color: #ff6b35;
}

.logo-img {
    width: 40px;
    height: 40px;
    object-fit: contain;
}

.logo-text {
    font-size: 24px;
    font-weight: bold;
}

.nav-menu {
    display: flex;
    gap: 30px;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    padding: 10px 15px;
    border-radius: 5px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.nav-link:hover,
.nav-link.active {
    color: #ff6b35;
    background: rgba(255, 107, 53, 0.1);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-box {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 25px;
    padding: 8px 15px;
    min-width: 250px;
}

.search-box input {
    border: none;
    background: none;
    outline: none;
    flex: 1;
    padding: 5px;
    font-size: 14px;
}

.search-box button {
    border: none;
    background: none;
    color: #666;
    cursor: pointer;
    padding: 5px;
}

.search-box button:hover {
    color: #ff6b35;
}

.cart-icon {
    position: relative;
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.cart-icon:hover {
    background: rgba(255, 107, 53, 0.1);
    color: #ff6b35;
}

.cart-icon i {
    font-size: 20px;
}

.cart-count {
    position: absolute;
    top: 0;
    right: 0;
    background: #ff6b35;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.user-menu {
    position: relative;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 10px 15px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.user-info:hover {
    background: rgba(255, 107, 53, 0.1);
    color: #ff6b35;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.user-menu:hover .user-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-dropdown a {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
}

.user-dropdown a:hover {
    background: rgba(255, 107, 53, 0.1);
    color: #ff6b35;
}

.user-dropdown a:first-child {
    border-radius: 8px 8px 0 0;
}

.user-dropdown a:last-child {
    border-radius: 0 0 8px 8px;
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 5px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Main Content */
.main-content {
    margin-top: 70px;
    min-height: calc(100vh - 70px);
}

.page {
    display: none;
    padding: 40px 20px;
}

.page.active {
    display: block;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 90px;
    right: 20px;
    z-index: 10000;
}

.toast {
    background: white;
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 10px;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 300px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast.hide {
    opacity: 0;
    transform: translateX(100%);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.toast-success {
    border-left: 4px solid #28a745;
}

.toast-success i {
    color: #28a745;
}

.toast-error {
    border-left: 4px solid #dc3545;
}

.toast-error i {
    color: #dc3545;
}

.toast-warning {
    border-left: 4px solid #ffc107;
}

.toast-warning i {
    color: #ffc107;
}

.toast-info {
    border-left: 4px solid #17a2b8;
}

.toast-info i {
    color: #17a2b8;
}

.toast-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #999;
    padding: 0;
    margin-left: 15px;
}

.toast-close:hover {
    color: #333;
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .search-box {
        min-width: 150px;
    }
    
    .nav-actions {
        gap: 10px;
    }
    
    .toast {
        min-width: 250px;
    }
    
    .toast-container {
        right: 10px;
        left: 10px;
    }
}
