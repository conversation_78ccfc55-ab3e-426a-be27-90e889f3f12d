// Admin Export Module
const AdminExport = {
    currentFilters: {
        dateFrom: '',
        dateTo: '',
        status: '',
        paymentStatus: '',
        format: 'pdf'
    },

    init() {
        this.bindEvents();
        this.setDefaultDateRange();
    },

    bindEvents() {
        // Export format selection
        const exportOptions = document.querySelectorAll('.export-option');
        exportOptions.forEach(option => {
            option.addEventListener('click', () => {
                this.selectExportFormat(option);
            });
        });

        // Filter inputs
        const dateFromInput = document.getElementById('export-date-from');
        const dateToInput = document.getElementById('export-date-to');
        const statusFilter = document.getElementById('export-status-filter');
        const paymentStatusFilter = document.getElementById('export-payment-status-filter');

        if (dateFromInput) {
            dateFromInput.addEventListener('change', () => {
                this.currentFilters.dateFrom = dateFromInput.value;
            });
        }

        if (dateToInput) {
            dateToInput.addEventListener('change', () => {
                this.currentFilters.dateTo = dateToInput.value;
            });
        }

        if (statusFilter) {
            statusFilter.addEventListener('change', () => {
                this.currentFilters.status = statusFilter.value;
            });
        }

        if (paymentStatusFilter) {
            paymentStatusFilter.addEventListener('change', () => {
                this.currentFilters.paymentStatus = paymentStatusFilter.value;
            });
        }

        // Export button
        const exportBtn = document.getElementById('start-export-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.startExport();
            });
        }

        // Clear filters button
        const clearBtn = document.getElementById('clear-filters-btn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearFilters();
            });
        }
    },

    setDefaultDateRange() {
        const today = new Date();
        const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
        
        const dateFromInput = document.getElementById('export-date-from');
        const dateToInput = document.getElementById('export-date-to');

        if (dateFromInput) {
            dateFromInput.value = lastMonth.toISOString().split('T')[0];
            this.currentFilters.dateFrom = dateFromInput.value;
        }

        if (dateToInput) {
            dateToInput.value = today.toISOString().split('T')[0];
            this.currentFilters.dateTo = dateToInput.value;
        }
    },

    selectExportFormat(selectedOption) {
        // Remove selected class from all options
        document.querySelectorAll('.export-option').forEach(option => {
            option.classList.remove('selected');
        });

        // Add selected class to clicked option
        selectedOption.classList.add('selected');

        // Update current format
        this.currentFilters.format = selectedOption.dataset.format;
    },

    async startExport() {
        try {
            this.showProgress();
            
            const params = new URLSearchParams();
            Object.keys(this.currentFilters).forEach(key => {
                if (this.currentFilters[key] && key !== 'format') {
                    params.append(key, this.currentFilters[key]);
                }
            });

            let endpoint = '';
            let filename = '';
            
            switch (this.currentFilters.format) {
                case 'pdf':
                    endpoint = '/api/orders/export/pdf';
                    filename = `orders_${new Date().toISOString().split('T')[0]}.pdf`;
                    break;
                case 'csv':
                    endpoint = '/api/orders/export/csv';
                    filename = `orders_${new Date().toISOString().split('T')[0]}.csv`;
                    break;
                case 'excel':
                    endpoint = '/api/orders/export/excel';
                    filename = `orders_${new Date().toISOString().split('T')[0]}.xlsx`;
                    break;
                default:
                    throw new Error('Invalid export format');
            }

            // Simulate progress
            this.updateProgress(25, 'Đang chuẩn bị dữ liệu...');
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            this.updateProgress(50, 'Đang xử lý dữ liệu...');

            // Make API request
            const response = await fetch(`${endpoint}?${params}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
                }
            });

            if (!response.ok) {
                throw new Error('Export failed');
            }

            this.updateProgress(75, 'Đang tạo file...');
            await new Promise(resolve => setTimeout(resolve, 500));

            // Download file
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            this.updateProgress(100, 'Hoàn thành!');
            
            setTimeout(() => {
                this.hideProgress();
                Toast.success('Xuất dữ liệu thành công!');
                this.addToHistory(this.currentFilters.format, filename);
            }, 1000);

        } catch (error) {
            console.error('Export error:', error);
            this.hideProgress();
            Toast.error('Có lỗi xảy ra khi xuất dữ liệu: ' + error.message);
        }
    },

    showProgress() {
        const progressContainer = document.getElementById('export-progress');
        if (progressContainer) {
            progressContainer.classList.add('active');
            this.updateProgress(0, 'Bắt đầu xuất dữ liệu...');
        }
    },

    hideProgress() {
        const progressContainer = document.getElementById('export-progress');
        if (progressContainer) {
            progressContainer.classList.remove('active');
        }
    },

    updateProgress(percentage, message) {
        const progressFill = document.querySelector('.progress-fill');
        const progressText = document.querySelector('.progress-text');
        const progressMessage = document.querySelector('.progress-info p');

        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
        }

        if (progressText) {
            progressText.textContent = `${percentage}%`;
        }

        if (progressMessage) {
            progressMessage.textContent = message;
        }
    },

    clearFilters() {
        // Reset filters
        this.currentFilters = {
            dateFrom: '',
            dateTo: '',
            status: '',
            paymentStatus: '',
            format: 'pdf'
        };

        // Reset form inputs
        const dateFromInput = document.getElementById('export-date-from');
        const dateToInput = document.getElementById('export-date-to');
        const statusFilter = document.getElementById('export-status-filter');
        const paymentStatusFilter = document.getElementById('export-payment-status-filter');

        if (dateFromInput) dateFromInput.value = '';
        if (dateToInput) dateToInput.value = '';
        if (statusFilter) statusFilter.value = '';
        if (paymentStatusFilter) paymentStatusFilter.value = '';

        // Reset export format selection
        document.querySelectorAll('.export-option').forEach(option => {
            option.classList.remove('selected');
        });

        const pdfOption = document.querySelector('.export-option[data-format="pdf"]');
        if (pdfOption) {
            pdfOption.classList.add('selected');
        }

        // Set default date range
        this.setDefaultDateRange();

        Toast.info('Đã xóa tất cả bộ lọc');
    },

    addToHistory(format, filename) {
        const historyContainer = document.getElementById('export-history-list');
        if (!historyContainer) return;

        const historyItem = document.createElement('div');
        historyItem.className = 'history-item';
        
        const formatIcons = {
            pdf: 'fas fa-file-pdf',
            csv: 'fas fa-file-csv',
            excel: 'fas fa-file-excel'
        };

        const formatNames = {
            pdf: 'PDF Document',
            csv: 'CSV Spreadsheet',
            excel: 'Excel Workbook'
        };

        historyItem.innerHTML = `
            <div class="history-info">
                <div class="history-icon ${format}">
                    <i class="${formatIcons[format]}"></i>
                </div>
                <div class="history-details">
                    <h4>${filename}</h4>
                    <p>${formatNames[format]} • ${new Date().toLocaleString('vi-VN')}</p>
                </div>
            </div>
            <div class="history-actions">
                <button class="history-btn download" onclick="AdminExport.downloadFromHistory('${filename}')">
                    <i class="fas fa-download"></i> Tải lại
                </button>
                <button class="history-btn delete" onclick="AdminExport.removeFromHistory(this)">
                    <i class="fas fa-trash"></i> Xóa
                </button>
            </div>
        `;

        // Add to top of history
        historyContainer.insertBefore(historyItem, historyContainer.firstChild);

        // Limit history to 10 items
        const historyItems = historyContainer.querySelectorAll('.history-item');
        if (historyItems.length > 10) {
            historyContainer.removeChild(historyItems[historyItems.length - 1]);
        }
    },

    downloadFromHistory(filename) {
        // In a real implementation, you would store the download URL
        // For now, just show a message
        Toast.info('Tính năng tải lại file sẽ được cập nhật trong phiên bản tiếp theo');
    },

    removeFromHistory(button) {
        const historyItem = button.closest('.history-item');
        if (historyItem) {
            historyItem.remove();
            Toast.success('Đã xóa khỏi lịch sử');
        }
    },

    // Quick export functions
    async quickExportPDF() {
        this.currentFilters.format = 'pdf';
        await this.startExport();
    },

    async quickExportCSV() {
        this.currentFilters.format = 'csv';
        await this.startExport();
    },

    async quickExportExcel() {
        this.currentFilters.format = 'excel';
        await this.startExport();
    }
};

// Export for global use
window.AdminExport = AdminExport;
