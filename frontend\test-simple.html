<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test - Na Food</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .error { color: red; background: #ffe6e6; }
        .success { color: green; background: #e6ffe6; }
        .info { color: blue; background: #e6f3ff; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🍜 Na Food - Simple Test Page</h1>
        
        <div class="test-section">
            <h2>📡 Server Connection Test</h2>
            <button onclick="testConnection()">Test Server Connection</button>
            <div id="connection-result"></div>
        </div>
        
        <div class="test-section">
            <h2>🔧 JavaScript Modules Test</h2>
            <button onclick="testJavaScript()">Test JavaScript Loading</button>
            <div id="js-result"></div>
        </div>
        
        <div class="test-section">
            <h2>🎨 CSS Loading Test</h2>
            <button onclick="testCSS()">Test CSS Loading</button>
            <div id="css-result"></div>
        </div>
        
        <div class="test-section">
            <h2>📊 API Test</h2>
            <button onclick="testAPI()">Test API Endpoints</button>
            <div id="api-result"></div>
        </div>
        
        <div class="test-section">
            <h2>🛠️ Console Errors</h2>
            <button onclick="checkConsoleErrors()">Check Console Errors</button>
            <div id="console-result"></div>
        </div>
    </div>

    <script>
        // Store console errors
        window.consoleErrors = [];
        const originalConsoleError = console.error;
        console.error = function(...args) {
            window.consoleErrors.push(args.join(' '));
            originalConsoleError.apply(console, arguments);
        };

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function testConnection() {
            try {
                const response = await fetch('/api/health');
                if (response.ok) {
                    const data = await response.json();
                    showResult('connection-result', `✅ Server Connected Successfully!<br>Message: ${data.message}`, 'success');
                } else {
                    showResult('connection-result', `❌ Server Error: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                showResult('connection-result', `❌ Connection Failed: ${error.message}`, 'error');
            }
        }

        function testJavaScript() {
            let results = [];
            
            // Test if utils.js loaded
            try {
                if (typeof API !== 'undefined') {
                    results.push('✅ utils.js (API) loaded');
                } else {
                    results.push('❌ utils.js (API) not loaded');
                }
            } catch (e) {
                results.push('❌ Error checking API: ' + e.message);
            }

            // Test basic JavaScript functionality
            try {
                const testArray = [1, 2, 3];
                const testResult = testArray.map(x => x * 2);
                results.push('✅ Basic JavaScript working');
            } catch (e) {
                results.push('❌ Basic JavaScript error: ' + e.message);
            }

            // Test fetch API
            if (typeof fetch !== 'undefined') {
                results.push('✅ Fetch API available');
            } else {
                results.push('❌ Fetch API not available');
            }

            showResult('js-result', results.join('<br>'), results.some(r => r.includes('❌')) ? 'error' : 'success');
        }

        function testCSS() {
            let results = [];
            
            // Check if stylesheets are loaded
            const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
            results.push(`📄 Found ${stylesheets.length} CSS files`);
            
            // Test if Font Awesome is loaded
            const testIcon = document.createElement('i');
            testIcon.className = 'fas fa-heart';
            testIcon.style.display = 'none';
            document.body.appendChild(testIcon);
            
            setTimeout(() => {
                const computedStyle = window.getComputedStyle(testIcon, ':before');
                if (computedStyle.content && computedStyle.content !== 'none') {
                    results.push('✅ Font Awesome loaded');
                } else {
                    results.push('❌ Font Awesome not loaded');
                }
                document.body.removeChild(testIcon);
                
                showResult('css-result', results.join('<br>'), results.some(r => r.includes('❌')) ? 'error' : 'success');
            }, 100);
        }

        async function testAPI() {
            let results = [];
            
            // Test Health API
            try {
                const healthResponse = await fetch('/api/health');
                if (healthResponse.ok) {
                    results.push('✅ Health API working');
                } else {
                    results.push('❌ Health API error: ' + healthResponse.status);
                }
            } catch (e) {
                results.push('❌ Health API failed: ' + e.message);
            }

            // Test Products API
            try {
                const productsResponse = await fetch('/api/products');
                if (productsResponse.ok) {
                    const data = await productsResponse.json();
                    results.push(`✅ Products API working (${data.data ? data.data.length : 0} products)`);
                } else {
                    results.push('❌ Products API error: ' + productsResponse.status);
                }
            } catch (e) {
                results.push('❌ Products API failed: ' + e.message);
            }

            showResult('api-result', results.join('<br>'), results.some(r => r.includes('❌')) ? 'error' : 'success');
        }

        function checkConsoleErrors() {
            if (window.consoleErrors.length === 0) {
                showResult('console-result', '✅ No console errors detected', 'success');
            } else {
                const errorList = window.consoleErrors.map(err => `• ${err}`).join('<br>');
                showResult('console-result', `❌ Console Errors Found:<br>${errorList}`, 'error');
            }
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', () => {
            console.log('🚀 Test page loaded');
            setTimeout(testConnection, 500);
            setTimeout(testJavaScript, 1000);
            setTimeout(testCSS, 1500);
        });
    </script>
</body>
</html>
