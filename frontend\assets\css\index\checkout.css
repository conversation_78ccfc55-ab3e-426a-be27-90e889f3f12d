/* Checkout Page Styles */

.checkout-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 40px;
}

.checkout-main {
    background: #fff;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.checkout-sidebar {
    background: #fff;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    height: fit-content;
    position: sticky;
    top: 20px;
}

.checkout-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 30px;
    text-align: center;
}

/* Form Styles */
.checkout-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.form-section {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 25px;
}

.section-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-title i {
    color: var(--primary-color);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-label {
    font-weight: 600;
    color: var(--text-dark);
    font-size: 0.95rem;
}

.form-input,
.form-select,
.form-textarea {
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

.form-error {
    color: var(--danger-color);
    font-size: 0.85rem;
    margin-top: 5px;
}

.form-group.error .form-input,
.form-group.error .form-select {
    border-color: var(--danger-color);
}

/* Payment Methods */
.payment-methods {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.payment-method {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 15px;
}

.payment-method:hover {
    border-color: var(--primary-color);
    background: #f8f9ff;
}

.payment-method.selected {
    border-color: var(--primary-color);
    background: #f8f9ff;
}

.payment-method input[type="radio"] {
    margin: 0;
}

.payment-method-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-light);
    border-radius: 8px;
    color: var(--primary-color);
    font-size: 1.2rem;
}

.payment-method-info h4 {
    margin: 0 0 5px 0;
    color: var(--text-dark);
    font-weight: 600;
}

.payment-method-info p {
    margin: 0;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.payment-method-fee {
    margin-left: auto;
    color: var(--success-color);
    font-weight: 600;
    font-size: 0.9rem;
}

/* Order Summary */
.order-summary {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.summary-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 20px;
    text-align: center;
}

.checkout-items {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.order-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.item-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 6px;
}

.item-info {
    flex: 1;
}

.item-name {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 5px;
}

.item-details {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.item-quantity {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.item-price {
    font-weight: 600;
    color: var(--primary-color);
}

/* Order Totals */
.order-totals {
    border-top: 1px solid #e9ecef;
    padding-top: 20px;
}

.total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.total-row.final {
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
    margin-top: 10px;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-dark);
}

.total-row.discount {
    color: var(--success-color);
}

/* Promo Code */
.promo-section {
    margin-bottom: 20px;
}

.promo-input-group {
    display: flex;
    gap: 10px;
}

.promo-input {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
}

.promo-btn {
    padding: 12px 20px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.promo-btn:hover {
    background: var(--primary-dark);
}

.promo-applied {
    display: none;
    background: var(--success-light);
    color: var(--success-color);
    padding: 10px 15px;
    border-radius: 8px;
    margin-top: 10px;
    align-items: center;
    gap: 10px;
}

.promo-applied.active {
    display: flex;
}

.remove-promo {
    background: none;
    border: none;
    color: var(--success-color);
    cursor: pointer;
    font-size: 1.2rem;
    margin-left: auto;
}

/* Submit Button */
.checkout-submit {
    width: 100%;
    padding: 18px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 20px;
}

.checkout-submit:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

.checkout-submit:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

/* Empty Cart */
.empty-cart {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-muted);
}

.empty-cart i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: var(--text-light);
}

.empty-cart h3 {
    margin-bottom: 10px;
    color: var(--text-dark);
}

/* Responsive Design */
@media (max-width: 768px) {
    .checkout-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .checkout-main,
    .checkout-sidebar {
        padding: 20px;
    }
    
    .payment-method {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .payment-method-fee {
        margin-left: 0;
    }
}
