const mongoose = require('mongoose');

const bannerSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Please add a banner title'],
    trim: true,
    maxlength: [100, 'Banner title cannot be more than 100 characters']
  },
  subtitle: {
    type: String,
    trim: true,
    maxlength: [200, 'Banner subtitle cannot be more than 200 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Banner description cannot be more than 500 characters']
  },
  image: {
    url: {
      type: String,
      required: [true, 'Please add a banner image']
    },
    alt: {
      type: String,
      default: ''
    }
  },
  mobileImage: {
    url: {
      type: String
    },
    alt: {
      type: String,
      default: ''
    }
  },
  link: {
    url: {
      type: String,
      trim: true
    },
    text: {
      type: String,
      trim: true,
      maxlength: [50, 'Link text cannot be more than 50 characters']
    },
    target: {
      type: String,
      enum: ['_self', '_blank'],
      default: '_self'
    }
  },
  type: {
    type: String,
    required: true,
    enum: ['hero', 'promotional', 'category', 'product', 'announcement'],
    default: 'promotional'
  },
  position: {
    type: String,
    enum: ['header', 'main', 'sidebar', 'footer'],
    default: 'main'
  },
  displayOrder: {
    type: Number,
    default: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },
  startDate: {
    type: Date,
    default: Date.now
  },
  endDate: {
    type: Date
  },
  targetAudience: {
    type: String,
    enum: ['all', 'new_users', 'returning_users', 'vip_users'],
    default: 'all'
  },
  backgroundColor: {
    type: String,
    default: '#ffffff',
    match: [/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Please provide a valid hex color']
  },
  textColor: {
    type: String,
    default: '#000000',
    match: [/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Please provide a valid hex color']
  },
  buttonStyle: {
    backgroundColor: {
      type: String,
      default: '#007bff',
      match: [/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Please provide a valid hex color']
    },
    textColor: {
      type: String,
      default: '#ffffff',
      match: [/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Please provide a valid hex color']
    },
    borderRadius: {
      type: Number,
      default: 5,
      min: 0,
      max: 50
    }
  },
  animation: {
    type: {
      type: String,
      enum: ['none', 'fade', 'slide', 'zoom'],
      default: 'none'
    },
    duration: {
      type: Number,
      default: 500,
      min: 100,
      max: 3000
    }
  },
  clickCount: {
    type: Number,
    default: 0
  },
  viewCount: {
    type: Number,
    default: 0
  },
  conversionRate: {
    type: Number,
    default: 0
  },
  createdBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  lastModifiedBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
bannerSchema.index({ isActive: 1, position: 1, displayOrder: 1 });
bannerSchema.index({ type: 1, isActive: 1 });
bannerSchema.index({ startDate: 1, endDate: 1 });
bannerSchema.index({ displayOrder: 1 });

// Virtual for checking if banner is currently active
bannerSchema.virtual('isCurrentlyActive').get(function() {
  const now = new Date();
  const isWithinDateRange = (!this.startDate || now >= this.startDate) && 
                           (!this.endDate || now <= this.endDate);
  return this.isActive && isWithinDateRange;
});

// Virtual for banner status
bannerSchema.virtual('status').get(function() {
  const now = new Date();
  
  if (!this.isActive) return 'inactive';
  if (this.startDate && now < this.startDate) return 'scheduled';
  if (this.endDate && now > this.endDate) return 'expired';
  return 'active';
});

// Virtual for performance metrics
bannerSchema.virtual('performanceMetrics').get(function() {
  return {
    clickThroughRate: this.viewCount > 0 ? (this.clickCount / this.viewCount * 100).toFixed(2) : 0,
    totalClicks: this.clickCount,
    totalViews: this.viewCount,
    conversionRate: this.conversionRate.toFixed(2)
  };
});

// Method to increment view count
bannerSchema.methods.incrementViewCount = function() {
  this.viewCount += 1;
  return this.save();
};

// Method to increment click count
bannerSchema.methods.incrementClickCount = function() {
  this.clickCount += 1;
  this.conversionRate = this.viewCount > 0 ? (this.clickCount / this.viewCount) * 100 : 0;
  return this.save();
};

// Method to activate/deactivate banner
bannerSchema.methods.toggleActive = function() {
  this.isActive = !this.isActive;
  return this.save();
};

// Method to update display order
bannerSchema.methods.updateDisplayOrder = function(newOrder) {
  this.displayOrder = newOrder;
  return this.save();
};

// Static method to get active banners by position
bannerSchema.statics.getActiveBannersByPosition = async function(position, targetAudience = 'all') {
  const now = new Date();
  
  const query = {
    isActive: true,
    position: position,
    $or: [
      { startDate: { $exists: false } },
      { startDate: { $lte: now } }
    ],
    $or: [
      { endDate: { $exists: false } },
      { endDate: { $gte: now } }
    ]
  };

  if (targetAudience !== 'all') {
    query.$or = [
      { targetAudience: 'all' },
      { targetAudience: targetAudience }
    ];
  }

  return await this.find(query)
    .sort({ displayOrder: 1, createdAt: -1 })
    .populate('createdBy', 'name')
    .populate('lastModifiedBy', 'name');
};

// Static method to get banner statistics
bannerSchema.statics.getBannerStats = async function(startDate, endDate) {
  const matchStage = {};
  
  if (startDate && endDate) {
    matchStage.createdAt = {
      $gte: startDate,
      $lte: endDate
    };
  }

  const stats = await this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalBanners: { $sum: 1 },
        activeBanners: {
          $sum: { $cond: [{ $eq: ['$isActive', true] }, 1, 0] }
        },
        totalViews: { $sum: '$viewCount' },
        totalClicks: { $sum: '$clickCount' },
        averageConversionRate: { $avg: '$conversionRate' }
      }
    },
    {
      $project: {
        totalBanners: 1,
        activeBanners: 1,
        totalViews: 1,
        totalClicks: 1,
        averageConversionRate: { $round: ['$averageConversionRate', 2] },
        overallClickThroughRate: {
          $round: [
            { $multiply: [{ $divide: ['$totalClicks', '$totalViews'] }, 100] },
            2
          ]
        }
      }
    }
  ]);

  return stats[0] || {
    totalBanners: 0,
    activeBanners: 0,
    totalViews: 0,
    totalClicks: 0,
    averageConversionRate: 0,
    overallClickThroughRate: 0
  };
};

// Pre-save middleware to update lastModifiedBy
bannerSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.lastModifiedBy = this.modifiedBy || this.createdBy;
  }
  next();
});

module.exports = mongoose.model('Banner', bannerSchema);
