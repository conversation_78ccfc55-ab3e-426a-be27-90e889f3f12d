/* Ad<PERSON> Login Styles */

.admin-login {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.login-container {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    width: 100%;
    max-width: 400px;
    text-align: center;
}

.login-header {
    margin-bottom: 40px;
}

.login-header .logo {
    width: 60px;
    height: 60px;
    margin-bottom: 20px;
}

.login-header h1 {
    color: #333;
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 10px;
}

.login-header p {
    color: #666;
    font-size: 16px;
}

.login-form {
    text-align: left;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 600;
    font-size: 14px;
}

.form-group input {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.login-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

.login-btn:active {
    transform: translateY(0);
}

.login-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Loading state */
.login-btn.loading {
    position: relative;
    color: transparent;
}

.login-btn.loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid transparent;
    border-top-color: white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error states */
.form-group.error input {
    border-color: #e74c3c;
    background: #fdf2f2;
}

.form-error {
    color: #e74c3c;
    font-size: 12px;
    margin-top: 5px;
    display: none;
}

.form-group.error .form-error {
    display: block;
}

/* Responsive */
@media (max-width: 480px) {
    .login-container {
        margin: 20px;
        padding: 30px 25px;
    }
    
    .login-header h1 {
        font-size: 24px;
    }
    
    .form-group input {
        padding: 12px 15px;
        font-size: 14px;
    }
    
    .login-btn {
        padding: 12px;
        font-size: 14px;
    }
}
