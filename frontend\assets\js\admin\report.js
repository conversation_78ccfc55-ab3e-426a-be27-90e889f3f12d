// Admin Report Module
const AdminReport = {
    currentDateRange: {
        from: '',
        to: ''
    },

    init() {
        this.bindEvents();
        this.setDefaultDateRange();
    },

    bindEvents() {
        const generateBtn = document.getElementById('generate-report-btn');
        if (generateBtn) {
            generateBtn.addEventListener('click', () => {
                this.generateReport();
            });
        }

        const dateFromInput = document.getElementById('report-date-from');
        const dateToInput = document.getElementById('report-date-to');

        if (dateFromInput) {
            dateFromInput.addEventListener('change', () => {
                this.currentDateRange.from = dateFromInput.value;
            });
        }

        if (dateToInput) {
            dateToInput.addEventListener('change', () => {
                this.currentDateRange.to = dateToInput.value;
            });
        }
    },

    setDefaultDateRange() {
        const today = new Date();
        const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
        
        const dateFromInput = document.getElementById('report-date-from');
        const dateToInput = document.getElementById('report-date-to');

        if (dateFromInput) {
            dateFromInput.value = lastMonth.toISOString().split('T')[0];
            this.currentDateRange.from = dateFromInput.value;
        }

        if (dateToInput) {
            dateToInput.value = today.toISOString().split('T')[0];
            this.currentDateRange.to = dateToInput.value;
        }
    },

    async loadReports() {
        this.generateReport();
    },

    async generateReport() {
        try {
            Loading.show();

            // Mock report data
            const reportData = {
                summary: {
                    totalOrders: 156,
                    totalRevenue: 12500000,
                    totalCustomers: 89,
                    avgOrderValue: 80128,
                    completionRate: 94.2
                },
                ordersByStatus: {
                    pending: 8,
                    confirmed: 12,
                    preparing: 15,
                    delivering: 6,
                    delivered: 147,
                    cancelled: 9
                },
                topProducts: [
                    { name: 'Phở Bò Tái', orders: 45, revenue: 2925000 },
                    { name: 'Cơm Tấm Sườn Nướng', orders: 38, revenue: 1710000 },
                    { name: 'Bánh Mì Thịt Nướng', orders: 52, revenue: 1300000 },
                    { name: 'Bún Bò Huế', orders: 28, revenue: 1680000 },
                    { name: 'Gỏi Cuốn Tôm Thịt', orders: 31, revenue: 930000 }
                ],
                revenueByDay: this.generateDailyRevenue(),
                customerStats: {
                    newCustomers: 23,
                    returningCustomers: 66,
                    customerRetentionRate: 74.2
                }
            };

            this.renderReport(reportData);

        } catch (error) {
            console.error('Generate report error:', error);
            Toast.error('Không thể tạo báo cáo');
        } finally {
            Loading.hide();
        }
    },

    renderReport(data) {
        const container = document.getElementById('reports-content');
        if (!container) return;

        container.innerHTML = `
            <div class="report-container">
                <!-- Summary Cards -->
                <div class="report-summary">
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="summary-info">
                            <h3>${data.summary.totalOrders}</h3>
                            <p>Tổng đơn hàng</p>
                        </div>
                    </div>
                    
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="summary-info">
                            <h3>${Utils.formatCurrency(data.summary.totalRevenue)}</h3>
                            <p>Tổng doanh thu</p>
                        </div>
                    </div>
                    
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="summary-info">
                            <h3>${data.summary.totalCustomers}</h3>
                            <p>Khách hàng</p>
                        </div>
                    </div>
                    
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="summary-info">
                            <h3>${Utils.formatCurrency(data.summary.avgOrderValue)}</h3>
                            <p>Giá trị TB/đơn</p>
                        </div>
                    </div>
                </div>

                <!-- Charts Section -->
                <div class="report-charts">
                    <div class="chart-section">
                        <h3>Doanh thu theo ngày</h3>
                        <canvas id="revenue-by-day-chart"></canvas>
                    </div>
                    
                    <div class="chart-section">
                        <h3>Đơn hàng theo trạng thái</h3>
                        <canvas id="orders-status-chart"></canvas>
                    </div>
                </div>

                <!-- Top Products -->
                <div class="report-section">
                    <h3>Top 5 món ăn bán chạy</h3>
                    <div class="top-products-table">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Món ăn</th>
                                    <th>Số đơn</th>
                                    <th>Doanh thu</th>
                                    <th>% Tổng doanh thu</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${data.topProducts.map((product, index) => `
                                    <tr>
                                        <td>
                                            <div class="product-rank">
                                                <span class="rank">#${index + 1}</span>
                                                <span class="name">${product.name}</span>
                                            </div>
                                        </td>
                                        <td>${product.orders}</td>
                                        <td>${Utils.formatCurrency(product.revenue)}</td>
                                        <td>${((product.revenue / data.summary.totalRevenue) * 100).toFixed(1)}%</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Customer Analytics -->
                <div class="report-section">
                    <h3>Phân tích khách hàng</h3>
                    <div class="customer-stats">
                        <div class="stat-item">
                            <span class="stat-label">Khách hàng mới:</span>
                            <span class="stat-value">${data.customerStats.newCustomers}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Khách hàng quay lại:</span>
                            <span class="stat-value">${data.customerStats.returningCustomers}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Tỷ lệ giữ chân:</span>
                            <span class="stat-value">${data.customerStats.customerRetentionRate}%</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Tỷ lệ hoàn thành:</span>
                            <span class="stat-value">${data.summary.completionRate}%</span>
                        </div>
                    </div>
                </div>

                <!-- Export Actions -->
                <div class="report-actions">
                    <button class="btn btn-primary" onclick="AdminReport.exportReportPDF()">
                        <i class="fas fa-file-pdf"></i> Xuất PDF
                    </button>
                    <button class="btn btn-secondary" onclick="AdminReport.exportReportCSV()">
                        <i class="fas fa-file-csv"></i> Xuất CSV
                    </button>
                </div>
            </div>
        `;

        // Render charts
        this.renderRevenueChart(data.revenueByDay);
        this.renderOrdersStatusChart(data.ordersByStatus);
    },

    renderRevenueChart(revenueData) {
        const ctx = document.getElementById('revenue-by-day-chart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: revenueData.map(item => item.date),
                datasets: [{
                    label: 'Doanh thu (VNĐ)',
                    data: revenueData.map(item => item.revenue),
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return Utils.formatCurrency(value);
                            }
                        }
                    }
                }
            }
        });
    },

    renderOrdersStatusChart(statusData) {
        const ctx = document.getElementById('orders-status-chart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Chờ xử lý', 'Đã xác nhận', 'Đang chuẩn bị', 'Đang giao', 'Đã giao', 'Đã hủy'],
                datasets: [{
                    data: [
                        statusData.pending,
                        statusData.confirmed,
                        statusData.preparing,
                        statusData.delivering,
                        statusData.delivered,
                        statusData.cancelled
                    ],
                    backgroundColor: [
                        '#ffc107',
                        '#17a2b8',
                        '#fd7e14',
                        '#6f42c1',
                        '#28a745',
                        '#dc3545'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    },

    generateDailyRevenue() {
        const days = [];
        const today = new Date();
        
        for (let i = 6; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            
            days.push({
                date: date.toLocaleDateString('vi-VN', { month: '2-digit', day: '2-digit' }),
                revenue: Math.floor(Math.random() * 2000000) + 500000
            });
        }
        
        return days;
    },

    async exportReportPDF() {
        try {
            Loading.show();

            const params = new URLSearchParams();
            if (this.currentDateRange.from) {
                params.append('dateFrom', this.currentDateRange.from);
            }
            if (this.currentDateRange.to) {
                params.append('dateTo', this.currentDateRange.to);
            }

            // Mock PDF export
            Toast.success('Đang tạo file PDF...');
            
            setTimeout(() => {
                Toast.success('Xuất báo cáo PDF thành công!');
            }, 2000);

        } catch (error) {
            console.error('Export PDF error:', error);
            Toast.error('Có lỗi xảy ra khi xuất PDF');
        } finally {
            Loading.hide();
        }
    },

    async exportReportCSV() {
        try {
            Loading.show();

            const params = new URLSearchParams();
            if (this.currentDateRange.from) {
                params.append('dateFrom', this.currentDateRange.from);
            }
            if (this.currentDateRange.to) {
                params.append('dateTo', this.currentDateRange.to);
            }

            // Mock CSV export
            Toast.success('Đang tạo file CSV...');
            
            setTimeout(() => {
                Toast.success('Xuất báo cáo CSV thành công!');
            }, 2000);

        } catch (error) {
            console.error('Export CSV error:', error);
            Toast.error('Có lỗi xảy ra khi xuất CSV');
        } finally {
            Loading.hide();
        }
    }
};

// Export for global use
window.AdminReport = AdminReport;
