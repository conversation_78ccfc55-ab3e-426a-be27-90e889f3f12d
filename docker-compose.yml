version: "3.9"

services:
  # Backend API Service
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: nafood-backend
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - MONGO_URI=***************************************************************
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - JWT_EXPIRE=7d
      - PORT=5000
    depends_on:
      - mongo
    volumes:
      - backend-uploads:/app/uploads
    networks:
      - nafood-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB Database Service
  mongo:
    image: mongo:7.0
    container_name: nafood-mongo
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password123
      - <PERSON><PERSON><PERSON><PERSON>_INITDB_DATABASE=nafood
    volumes:
      - mongo-data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - nafood-network
    restart: unless-stopped
    command: mongod --auth

  # Frontend Service (Nginx)
  frontend:
    image: nginx:alpine
    container_name: nafood-frontend
    ports:
      - "3000:80"
    volumes:
      - ./frontend:/usr/share/nginx/html:ro
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - backend
    networks:
      - nafood-network
    restart: unless-stopped

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: nafood-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - nafood-network
    restart: unless-stopped
    command: redis-server --appendonly yes

# Volumes
volumes:
  mongo-data:
    driver: local
  backend-uploads:
    driver: local
  redis-data:
    driver: local

# Networks
networks:
  nafood-network:
    driver: bridge
