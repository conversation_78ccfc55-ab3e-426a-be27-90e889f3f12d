/* Admin Review Management Styles */

.review-content {
    max-width: 300px;
}

.review-comment {
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

.rating-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.rating-stars {
    color: #ffc107;
    font-size: 14px;
}

.rating-stars i {
    margin-right: 2px;
}

.helpful-count {
    font-weight: 600;
    color: #007bff;
}

/* Review Status */
.status-approved {
    background: #d4edda;
    color: #155724;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-hidden {
    background: #f8d7da;
    color: #721c24;
}

/* Review Detail Modal */
.review-detail-modal {
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.review-detail {
    padding: 0;
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.product-info h3 {
    color: #333;
    margin-bottom: 10px;
    font-size: 20px;
}

.rating-display {
    display: flex;
    align-items: center;
    gap: 10px;
}

.rating-display .rating-stars {
    font-size: 18px;
}

.rating-number {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.user-info {
    text-align: right;
    color: #666;
}

.user-info strong {
    color: #333;
    display: block;
    margin-bottom: 4px;
}

.user-info small {
    display: block;
    font-size: 12px;
}

.review-content {
    margin-bottom: 25px;
}

.review-content h4 {
    color: #333;
    margin-bottom: 10px;
    font-size: 18px;
}

.review-content p {
    color: #666;
    line-height: 1.6;
    font-size: 15px;
}

.review-stats {
    display: flex;
    gap: 30px;
    margin-bottom: 25px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.stat-label {
    font-size: 12px;
    color: #666;
    font-weight: 600;
    text-transform: uppercase;
}

.stat-item span:last-child {
    font-weight: 600;
    color: #333;
}

.review-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.review-actions .btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

/* Review Filters */
.review-filters {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-size: 12px;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
}

.filter-group input,
.filter-group select {
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Action Buttons */
.btn-approve {
    background: #28a745;
    color: white;
}

.btn-approve:hover {
    background: #218838;
    transform: scale(1.1);
}

/* Review Statistics */
.review-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.summary-card .summary-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.summary-card.pending .summary-number {
    color: #ffc107;
}

.summary-card.approved .summary-number {
    color: #28a745;
}

.summary-card.hidden .summary-number {
    color: #dc3545;
}

.summary-card.total .summary-number {
    color: #007bff;
}

.summary-card .summary-label {
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .review-filters {
        grid-template-columns: 1fr;
    }
    
    .review-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .user-info {
        text-align: left;
    }
    
    .review-stats {
        flex-direction: column;
        gap: 15px;
    }
    
    .review-actions {
        flex-direction: column;
    }
    
    .review-summary {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .review-summary {
        grid-template-columns: 1fr;
    }
    
    .rating-display {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}
