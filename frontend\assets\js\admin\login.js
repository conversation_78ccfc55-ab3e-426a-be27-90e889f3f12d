// Admin Login Module
const AdminLogin = {
    init() {
        this.bindEvents();
        this.checkExistingAuth();
    },

    bindEvents() {
        const loginForm = document.getElementById('admin-login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin(e);
            });
        }
    },

    checkExistingAuth() {
        const token = localStorage.getItem('adminToken');
        if (token) {
            this.verifyToken(token);
        }
    },

    async verifyToken(token) {
        try {
            const response = await API.request('/auth/me', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (response.success && ['admin', 'staff'].includes(response.data.role)) {
                this.loginSuccess(response.data);
            } else {
                localStorage.removeItem('adminToken');
            }
        } catch (error) {
            localStorage.removeItem('adminToken');
        }
    },

    async handleLogin(e) {
        const formData = new FormData(e.target);
        const email = formData.get('email');
        const password = formData.get('password');

        if (!email || !password) {
            Toast.error('Vui lòng nhập đầy đủ thông tin');
            return;
        }

        try {
            Loading.show();

            const response = await API.post('/auth/login', {
                email,
                password
            });

            if (response.success) {
                const user = response.data.user;
                
                // Check if user has admin or staff role
                if (!['admin', 'staff'].includes(user.role)) {
                    Toast.error('Bạn không có quyền truy cập trang quản trị');
                    return;
                }

                // Store token
                localStorage.setItem('adminToken', response.data.token);
                
                this.loginSuccess(user);
                Toast.success(`Chào mừng ${user.name}!`);
            } else {
                Toast.error(response.error || 'Đăng nhập thất bại');
            }
        } catch (error) {
            console.error('Login error:', error);
            Toast.error('Có lỗi xảy ra khi đăng nhập');
        } finally {
            Loading.hide();
        }
    },

    loginSuccess(user) {
        // Hide login page
        document.getElementById('admin-login').style.display = 'none';
        
        // Show dashboard
        document.getElementById('admin-dashboard').style.display = 'flex';
        
        // Update user info
        document.getElementById('admin-user-name').textContent = user.name;
        document.getElementById('header-user-name').textContent = user.name;
        
        // Initialize admin modules
        if (typeof AdminDashboard !== 'undefined') AdminDashboard.init();
        if (typeof AdminProduct !== 'undefined') AdminProduct.init();
        if (typeof AdminOrder !== 'undefined') AdminOrder.init();
        if (typeof AdminUser !== 'undefined') AdminUser.init();
        if (typeof AdminReview !== 'undefined') AdminReview.init();
        if (typeof AdminBanner !== 'undefined') AdminBanner.init();
        if (typeof AdminReport !== 'undefined') AdminReport.init();
        if (typeof AdminExport !== 'undefined') AdminExport.init();
        
        // Load dashboard data
        if (typeof AdminDashboard !== 'undefined') {
            AdminDashboard.loadDashboardData();
        }
    },

    logout() {
        localStorage.removeItem('adminToken');
        
        // Show login page
        document.getElementById('admin-login').style.display = 'flex';
        
        // Hide dashboard
        document.getElementById('admin-dashboard').style.display = 'none';
        
        Toast.success('Đăng xuất thành công');
    }
};

// Export for global use
window.AdminLogin = AdminLogin;
