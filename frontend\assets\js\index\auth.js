// Authentication Module
const Auth = {
    currentUser: null,

    init() {
        this.checkAuthStatus();
        this.bindEvents();
    },

    bindEvents() {
        // Login/Register buttons in header
        const loginBtn = document.getElementById('login-btn');
        const registerBtn = document.getElementById('register-btn');
        const logoutBtn = document.getElementById('logout-btn');

        if (loginBtn) {
            loginBtn.addEventListener('click', () => {
                this.showLoginModal();
            });
        }

        if (registerBtn) {
            registerBtn.addEventListener('click', () => {
                this.showRegisterModal();
            });
        }

        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                this.logout();
            });
        }
    },

    async checkAuthStatus() {
        const token = Storage.get('token');
        
        if (!token) {
            this.updateAuthUI(false);
            return;
        }

        try {
            const response = await API.get('/auth/me');
            this.currentUser = response.data;
            this.updateAuthUI(true);
        } catch (error) {
            console.error('Auth check error:', error);
            Storage.remove('token');
            this.updateAuthUI(false);
        }
    },

    updateAuthUI(isLoggedIn) {
        const loginBtn = document.getElementById('login-btn');
        const registerBtn = document.getElementById('register-btn');
        const userMenu = document.getElementById('user-menu');
        const userNameSpan = document.getElementById('user-name');

        if (isLoggedIn && this.currentUser) {
            // Hide login/register buttons
            if (loginBtn) loginBtn.style.display = 'none';
            if (registerBtn) registerBtn.style.display = 'none';
            
            // Show user menu
            if (userMenu) userMenu.style.display = 'block';
            if (userNameSpan) userNameSpan.textContent = this.currentUser.name;
        } else {
            // Show login/register buttons
            if (loginBtn) loginBtn.style.display = 'inline-block';
            if (registerBtn) registerBtn.style.display = 'inline-block';
            
            // Hide user menu
            if (userMenu) userMenu.style.display = 'none';
        }
    },

    showLoginModal() {
        const modalContent = `
            <div class="auth-modal">
                <div class="modal-header">
                    <h2>Đăng nhập</h2>
                    <button class="modal-close" onclick="Modal.hide()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="login-form" class="auth-form">
                        <div class="form-group">
                            <label>Email *</label>
                            <input type="email" name="email" required placeholder="Nhập email của bạn">
                            <div class="form-error"></div>
                        </div>
                        
                        <div class="form-group">
                            <label>Mật khẩu *</label>
                            <input type="password" name="password" required placeholder="Nhập mật khẩu">
                            <div class="form-error"></div>
                        </div>
                        
                        <div class="form-options">
                            <label class="checkbox-label">
                                <input type="checkbox" name="remember">
                                Ghi nhớ đăng nhập
                            </label>
                            <a href="#" onclick="Auth.showForgotPasswordModal()">Quên mật khẩu?</a>
                        </div>
                        
                        <button type="submit" class="auth-btn">
                            <i class="fas fa-sign-in-alt"></i>
                            Đăng nhập
                        </button>
                    </form>
                    
                    <div class="auth-divider">
                        <span>hoặc</span>
                    </div>
                    
                    <div class="social-login">
                        <button class="social-btn google-btn" onclick="Auth.loginWithGoogle()">
                            <i class="fab fa-google"></i>
                            Đăng nhập với Google
                        </button>
                        <button class="social-btn facebook-btn" onclick="Auth.loginWithFacebook()">
                            <i class="fab fa-facebook-f"></i>
                            Đăng nhập với Facebook
                        </button>
                    </div>
                    
                    <div class="auth-switch">
                        Chưa có tài khoản? 
                        <a href="#" onclick="Auth.showRegisterModal()">Đăng ký ngay</a>
                    </div>
                </div>
            </div>
        `;

        Modal.show(modalContent);

        // Bind form submit
        document.getElementById('login-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin(e.target);
        });
    },

    showRegisterModal() {
        const modalContent = `
            <div class="auth-modal">
                <div class="modal-header">
                    <h2>Đăng ký</h2>
                    <button class="modal-close" onclick="Modal.hide()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="register-form" class="auth-form">
                        <div class="form-group">
                            <label>Họ và tên *</label>
                            <input type="text" name="name" required placeholder="Nhập họ và tên">
                            <div class="form-error"></div>
                        </div>
                        
                        <div class="form-group">
                            <label>Email *</label>
                            <input type="email" name="email" required placeholder="Nhập email của bạn">
                            <div class="form-error"></div>
                        </div>
                        
                        <div class="form-group">
                            <label>Số điện thoại</label>
                            <input type="tel" name="phone" placeholder="Nhập số điện thoại">
                            <div class="form-error"></div>
                        </div>
                        
                        <div class="form-group">
                            <label>Mật khẩu *</label>
                            <input type="password" name="password" required placeholder="Nhập mật khẩu (tối thiểu 6 ký tự)">
                            <div class="form-error"></div>
                        </div>
                        
                        <div class="form-group">
                            <label>Xác nhận mật khẩu *</label>
                            <input type="password" name="confirmPassword" required placeholder="Nhập lại mật khẩu">
                            <div class="form-error"></div>
                        </div>
                        
                        <div class="form-options">
                            <label class="checkbox-label">
                                <input type="checkbox" name="terms" required>
                                Tôi đồng ý với <a href="#" onclick="App.showView('terms')">Điều khoản sử dụng</a>
                            </label>
                        </div>
                        
                        <button type="submit" class="auth-btn">
                            <i class="fas fa-user-plus"></i>
                            Đăng ký
                        </button>
                    </form>
                    
                    <div class="auth-switch">
                        Đã có tài khoản? 
                        <a href="#" onclick="Auth.showLoginModal()">Đăng nhập ngay</a>
                    </div>
                </div>
            </div>
        `;

        Modal.show(modalContent);

        // Bind form submit
        document.getElementById('register-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleRegister(e.target);
        });
    },

    showForgotPasswordModal() {
        const modalContent = `
            <div class="auth-modal">
                <div class="modal-header">
                    <h2>Quên mật khẩu</h2>
                    <button class="modal-close" onclick="Modal.hide()">&times;</button>
                </div>
                <div class="modal-body">
                    <p>Nhập email của bạn để nhận liên kết đặt lại mật khẩu:</p>
                    
                    <form id="forgot-password-form" class="auth-form">
                        <div class="form-group">
                            <label>Email *</label>
                            <input type="email" name="email" required placeholder="Nhập email của bạn">
                            <div class="form-error"></div>
                        </div>
                        
                        <button type="submit" class="auth-btn">
                            <i class="fas fa-paper-plane"></i>
                            Gửi liên kết
                        </button>
                    </form>
                    
                    <div class="auth-switch">
                        Nhớ mật khẩu? 
                        <a href="#" onclick="Auth.showLoginModal()">Đăng nhập ngay</a>
                    </div>
                </div>
            </div>
        `;

        Modal.show(modalContent);

        // Bind form submit
        document.getElementById('forgot-password-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleForgotPassword(e.target);
        });
    },

    async handleLogin(form) {
        try {
            const formData = new FormData(form);
            const loginData = {
                email: formData.get('email'),
                password: formData.get('password')
            };

            // Validate form
            if (!this.validateLoginForm(loginData)) {
                return;
            }

            Loading.show();

            const response = await API.post('/auth/login', loginData);
            
            // Store token
            Storage.set('token', response.data.token);
            this.currentUser = response.data.user;

            Modal.hide();
            Toast.success(`Chào mừng ${this.currentUser.name}!`);
            this.updateAuthUI(true);

        } catch (error) {
            console.error('Login error:', error);
            this.showFormError(form, error.message || 'Đăng nhập thất bại');
        } finally {
            Loading.hide();
        }
    },

    async handleRegister(form) {
        try {
            const formData = new FormData(form);
            const registerData = {
                name: formData.get('name'),
                email: formData.get('email'),
                phone: formData.get('phone'),
                password: formData.get('password'),
                confirmPassword: formData.get('confirmPassword')
            };

            // Validate form
            if (!this.validateRegisterForm(registerData)) {
                return;
            }

            Loading.show();

            const response = await API.post('/auth/register', registerData);
            
            // Store token
            Storage.set('token', response.data.token);
            this.currentUser = response.data.user;

            Modal.hide();
            Toast.success(`Đăng ký thành công! Chào mừng ${this.currentUser.name}!`);
            this.updateAuthUI(true);

        } catch (error) {
            console.error('Register error:', error);
            this.showFormError(form, error.message || 'Đăng ký thất bại');
        } finally {
            Loading.hide();
        }
    },

    async handleForgotPassword(form) {
        try {
            const formData = new FormData(form);
            const email = formData.get('email');

            if (!Utils.isValidEmail(email)) {
                this.showFieldError(form.querySelector('input[name="email"]'), 'Email không hợp lệ');
                return;
            }

            Loading.show();

            // Mock API call - in real app would send reset email
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            Modal.hide();
            Toast.success('Liên kết đặt lại mật khẩu đã được gửi đến email của bạn!');

        } catch (error) {
            console.error('Forgot password error:', error);
            this.showFormError(form, 'Có lỗi xảy ra. Vui lòng thử lại.');
        } finally {
            Loading.hide();
        }
    },

    validateLoginForm(data) {
        let isValid = true;
        const form = document.getElementById('login-form');

        // Email validation
        if (!data.email) {
            this.showFieldError(form.querySelector('input[name="email"]'), 'Email là bắt buộc');
            isValid = false;
        } else if (!Utils.isValidEmail(data.email)) {
            this.showFieldError(form.querySelector('input[name="email"]'), 'Email không hợp lệ');
            isValid = false;
        }

        // Password validation
        if (!data.password) {
            this.showFieldError(form.querySelector('input[name="password"]'), 'Mật khẩu là bắt buộc');
            isValid = false;
        }

        return isValid;
    },

    validateRegisterForm(data) {
        let isValid = true;
        const form = document.getElementById('register-form');

        // Name validation
        if (!data.name || data.name.length < 2) {
            this.showFieldError(form.querySelector('input[name="name"]'), 'Tên phải có ít nhất 2 ký tự');
            isValid = false;
        }

        // Email validation
        if (!data.email) {
            this.showFieldError(form.querySelector('input[name="email"]'), 'Email là bắt buộc');
            isValid = false;
        } else if (!Utils.isValidEmail(data.email)) {
            this.showFieldError(form.querySelector('input[name="email"]'), 'Email không hợp lệ');
            isValid = false;
        }

        // Phone validation (optional)
        if (data.phone && !Utils.isValidPhone(data.phone)) {
            this.showFieldError(form.querySelector('input[name="phone"]'), 'Số điện thoại không hợp lệ');
            isValid = false;
        }

        // Password validation
        if (!data.password) {
            this.showFieldError(form.querySelector('input[name="password"]'), 'Mật khẩu là bắt buộc');
            isValid = false;
        } else if (data.password.length < 6) {
            this.showFieldError(form.querySelector('input[name="password"]'), 'Mật khẩu phải có ít nhất 6 ký tự');
            isValid = false;
        }

        // Confirm password validation
        if (data.password !== data.confirmPassword) {
            this.showFieldError(form.querySelector('input[name="confirmPassword"]'), 'Mật khẩu xác nhận không khớp');
            isValid = false;
        }

        return isValid;
    },

    showFieldError(field, message) {
        const formGroup = field.closest('.form-group');
        const errorElement = formGroup.querySelector('.form-error');
        
        formGroup.classList.add('error');
        errorElement.textContent = message;
    },

    showFormError(form, message) {
        Toast.error(message);
    },

    async logout() {
        try {
            // Clear token
            Storage.remove('token');
            this.currentUser = null;
            
            // Update UI
            this.updateAuthUI(false);
            
            // Clear cart if needed
            Cart.clear();
            
            Toast.success('Đã đăng xuất thành công');
            
            // Navigate to home
            App.showView('home');

        } catch (error) {
            console.error('Logout error:', error);
        }
    },

    // Social login methods (mock implementations)
    async loginWithGoogle() {
        Toast.info('Tính năng đăng nhập Google sẽ được cập nhật sớm');
    },

    async loginWithFacebook() {
        Toast.info('Tính năng đăng nhập Facebook sẽ được cập nhật sớm');
    },

    // Check if user is logged in
    isLoggedIn() {
        return !!this.currentUser && !!Storage.get('token');
    },

    // Get current user
    getCurrentUser() {
        return this.currentUser;
    },

    // Require authentication for protected pages
    requireAuth() {
        if (!this.isLoggedIn()) {
            this.showLoginModal();
            return false;
        }
        return true;
    }
};

// Export for global use
window.Auth = Auth;
