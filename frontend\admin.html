<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Na Food Admin - Quản lý hệ thống">
    <title>Na Food Admin - Quản lý hệ thống</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/admin/dashboard.css">
    <link rel="stylesheet" href="assets/css/admin/product.css">
    <link rel="stylesheet" href="assets/css/admin/order.css">
    <link rel="stylesheet" href="assets/css/admin/user.css">
    <link rel="stylesheet" href="assets/css/admin/review.css">
    <link rel="stylesheet" href="assets/css/admin/banner.css">
    <link rel="stylesheet" href="assets/css/admin/report.css">
    <link rel="stylesheet" href="assets/css/admin/export.css">
    <link rel="stylesheet" href="assets/css/admin/login.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Loading Spinner -->
    <div id="loading-spinner" class="loading-spinner">
        <div class="spinner"></div>
        <p>Đang tải...</p>
    </div>

    <!-- Admin Login Page -->
    <div id="admin-login" class="admin-login">
        <div class="login-container">
            <div class="login-header">
                <img src="/images/logo.png" alt="Na Food" class="logo">
                <h1>Na Food Admin</h1>
                <p>Đăng nhập để quản lý hệ thống</p>
            </div>
            
            <form id="admin-login-form" class="login-form">
                <div class="form-group">
                    <label for="admin-email">Email</label>
                    <input type="email" id="admin-email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="admin-password">Mật khẩu</label>
                    <input type="password" id="admin-password" name="password" required>
                </div>
                
                <button type="submit" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i> Đăng nhập
                </button>
            </form>
        </div>
    </div>

    <!-- Admin Dashboard -->
    <div id="admin-dashboard" class="admin-dashboard" style="display: none;">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="/images/logo.png" alt="Na Food" class="logo">
                <h2>Na Food Admin</h2>
            </div>
            
            <nav class="sidebar-nav">
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#dashboard" class="nav-link active" data-page="dashboard">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#products" class="nav-link" data-page="products">
                            <i class="fas fa-utensils"></i>
                            <span>Quản lý món ăn</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#orders" class="nav-link" data-page="orders">
                            <i class="fas fa-shopping-cart"></i>
                            <span>Quản lý đơn hàng</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#users" class="nav-link" data-page="users">
                            <i class="fas fa-users"></i>
                            <span>Quản lý người dùng</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#reviews" class="nav-link" data-page="reviews">
                            <i class="fas fa-star"></i>
                            <span>Quản lý đánh giá</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#banners" class="nav-link" data-page="banners">
                            <i class="fas fa-image"></i>
                            <span>Quản lý banner</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#reports" class="nav-link" data-page="reports">
                            <i class="fas fa-chart-bar"></i>
                            <span>Báo cáo thống kê</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#export" class="nav-link" data-page="export">
                            <i class="fas fa-download"></i>
                            <span>Xuất dữ liệu</span>
                        </a>
                    </li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <div class="user-info">
                    <i class="fas fa-user-circle"></i>
                    <span id="admin-user-name">Admin</span>
                </div>
                <button id="admin-logout" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    Đăng xuất
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="main-header">
                <div class="header-left">
                    <button id="sidebar-toggle" class="sidebar-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 id="page-title">Dashboard</h1>
                </div>
                
                <div class="header-right">
                    <div class="notifications">
                        <button class="notification-btn">
                            <i class="fas fa-bell"></i>
                            <span class="notification-count">3</span>
                        </button>
                    </div>
                    
                    <div class="user-menu">
                        <button class="user-menu-btn">
                            <i class="fas fa-user-circle"></i>
                            <span id="header-user-name">Admin</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <div class="page-content">
                <!-- Dashboard Page -->
                <section id="dashboard-page" class="admin-page active">
                    <div class="dashboard-stats">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="total-orders">0</h3>
                                <p>Tổng đơn hàng</p>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="total-revenue">0đ</h3>
                                <p>Doanh thu</p>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-utensils"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="total-products">0</h3>
                                <p>Món ăn</p>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="total-users">0</h3>
                                <p>Người dùng</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="dashboard-charts">
                        <div class="chart-container">
                            <h3>Doanh thu theo tháng</h3>
                            <canvas id="revenue-chart"></canvas>
                        </div>
                        
                        <div class="chart-container">
                            <h3>Đơn hàng theo trạng thái</h3>
                            <canvas id="orders-chart"></canvas>
                        </div>
                    </div>
                    
                    <div class="recent-orders">
                        <h3>Đơn hàng gần đây</h3>
                        <div id="recent-orders-table">
                            <!-- Recent orders table will be loaded here -->
                        </div>
                    </div>
                </section>

                <!-- Products Management Page -->
                <section id="products-page" class="admin-page">
                    <div class="page-header">
                        <h2>Quản lý món ăn</h2>
                        <button id="add-product-btn" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Thêm món ăn
                        </button>
                    </div>
                    
                    <div class="page-filters">
                        <input type="text" id="product-search" placeholder="Tìm kiếm món ăn...">
                        <select id="product-category-filter">
                            <option value="">Tất cả danh mục</option>
                        </select>
                        <select id="product-status-filter">
                            <option value="">Tất cả trạng thái</option>
                            <option value="true">Có sẵn</option>
                            <option value="false">Hết hàng</option>
                        </select>
                    </div>
                    
                    <div id="products-table" class="data-table">
                        <!-- Products table will be loaded here -->
                    </div>
                </section>

                <!-- Orders Management Page -->
                <section id="orders-page" class="admin-page">
                    <div class="page-header">
                        <h2>Quản lý đơn hàng</h2>
                        <div class="header-actions">
                            <button id="export-orders-btn" class="btn btn-secondary">
                                <i class="fas fa-download"></i> Xuất Excel
                            </button>
                        </div>
                    </div>
                    
                    <div class="page-filters">
                        <input type="text" id="order-search" placeholder="Tìm kiếm đơn hàng...">
                        <select id="order-status-filter">
                            <option value="">Tất cả trạng thái</option>
                            <option value="pending">Chờ xử lý</option>
                            <option value="confirmed">Đã xác nhận</option>
                            <option value="preparing">Đang chuẩn bị</option>
                            <option value="delivering">Đang giao</option>
                            <option value="delivered">Đã giao</option>
                            <option value="cancelled">Đã hủy</option>
                        </select>
                        <input type="date" id="order-date-from">
                        <input type="date" id="order-date-to">
                    </div>
                    
                    <div id="orders-table" class="data-table">
                        <!-- Orders table will be loaded here -->
                    </div>
                </section>

                <!-- Users Management Page -->
                <section id="users-page" class="admin-page">
                    <div class="page-header">
                        <h2>Quản lý người dùng</h2>
                    </div>
                    
                    <div class="page-filters">
                        <input type="text" id="user-search" placeholder="Tìm kiếm người dùng...">
                        <select id="user-role-filter">
                            <option value="">Tất cả vai trò</option>
                            <option value="user">Khách hàng</option>
                            <option value="staff">Nhân viên</option>
                            <option value="admin">Quản trị viên</option>
                        </select>
                        <select id="user-status-filter">
                            <option value="">Tất cả trạng thái</option>
                            <option value="true">Hoạt động</option>
                            <option value="false">Bị khóa</option>
                        </select>
                    </div>
                    
                    <div id="users-table" class="data-table">
                        <!-- Users table will be loaded here -->
                    </div>
                </section>

                <!-- Reviews Management Page -->
                <section id="reviews-page" class="admin-page">
                    <div class="page-header">
                        <h2>Quản lý đánh giá</h2>
                    </div>
                    
                    <div class="page-filters">
                        <input type="text" id="review-search" placeholder="Tìm kiếm đánh giá...">
                        <select id="review-status-filter">
                            <option value="">Tất cả trạng thái</option>
                            <option value="pending">Chờ duyệt</option>
                            <option value="approved">Đã duyệt</option>
                            <option value="hidden">Đã ẩn</option>
                        </select>
                        <select id="review-rating-filter">
                            <option value="">Tất cả đánh giá</option>
                            <option value="5">5 sao</option>
                            <option value="4">4 sao</option>
                            <option value="3">3 sao</option>
                            <option value="2">2 sao</option>
                            <option value="1">1 sao</option>
                        </select>
                    </div>
                    
                    <div id="reviews-table" class="data-table">
                        <!-- Reviews table will be loaded here -->
                    </div>
                </section>

                <!-- Banners Management Page -->
                <section id="banners-page" class="admin-page">
                    <div class="page-header">
                        <h2>Quản lý banner</h2>
                        <button id="add-banner-btn" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Thêm banner
                        </button>
                    </div>
                    
                    <div id="banners-table" class="data-table">
                        <!-- Banners table will be loaded here -->
                    </div>
                </section>

                <!-- Reports Page -->
                <section id="reports-page" class="admin-page">
                    <div class="page-header">
                        <h2>Báo cáo thống kê</h2>
                        <div class="date-range-picker">
                            <input type="date" id="report-date-from">
                            <input type="date" id="report-date-to">
                            <button id="generate-report-btn" class="btn btn-primary">Tạo báo cáo</button>
                        </div>
                    </div>
                    
                    <div id="reports-content" class="reports-content">
                        <!-- Reports content will be loaded here -->
                    </div>
                </section>

                <!-- Export Page -->
                <section id="export-page" class="admin-page">
                    <div class="page-header">
                        <h2>Xuất dữ liệu</h2>
                    </div>
                    
                    <div id="export-content" class="export-content">
                        <!-- Export options will be loaded here -->
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- Modals -->
    <div id="modal-overlay" class="modal-overlay">
        <div id="modal-content" class="modal-content">
            <!-- Modal content will be loaded here -->
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container">
        <!-- Toast notifications will appear here -->
    </div>

    <!-- JavaScript Files -->
    <script src="assets/js/utils.js"></script>
    <script src="assets/js/admin/login.js"></script>
    <script src="assets/js/admin/dashboard.js"></script>
    <script src="assets/js/admin/product.js"></script>
    <script src="assets/js/admin/order.js"></script>
    <script src="assets/js/admin/user.js"></script>
    <script src="assets/js/admin/review.js"></script>
    <script src="assets/js/admin/banner.js"></script>
    <script src="assets/js/admin/report.js"></script>
    <script src="assets/js/admin/export.js"></script>
    
    <!-- Main Admin App Script -->
    <script>
        // Initialize admin app when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in
            const token = Storage.get('adminToken');
            if (token) {
                // Initialize admin modules
                if (typeof AdminDashboard !== 'undefined') AdminDashboard.init();
                if (typeof AdminProduct !== 'undefined') AdminProduct.init();
                if (typeof AdminOrder !== 'undefined') AdminOrder.init();
                if (typeof AdminUser !== 'undefined') AdminUser.init();
                if (typeof AdminReview !== 'undefined') AdminReview.init();
                if (typeof AdminBanner !== 'undefined') AdminBanner.init();
                if (typeof AdminReport !== 'undefined') AdminReport.init();
                if (typeof AdminExport !== 'undefined') AdminExport.init();
                
                // Show dashboard
                document.getElementById('admin-login').style.display = 'none';
                document.getElementById('admin-dashboard').style.display = 'flex';
            } else {
                // Initialize login
                if (typeof AdminLogin !== 'undefined') AdminLogin.init();
            }
            
            // Hide loading spinner
            document.getElementById('loading-spinner').style.display = 'none';
        });
    </script>
</body>
</html>
