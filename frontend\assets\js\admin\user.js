// Admin User Management Module
const AdminUser = {
    users: [],
    currentFilters: {
        search: '',
        role: '',
        status: '',
        page: 1,
        limit: 20
    },

    init() {
        this.bindEvents();
    },

    bindEvents() {
        // Search and filters
        const searchInput = document.getElementById('user-search');
        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce(() => {
                this.currentFilters.search = searchInput.value;
                this.currentFilters.page = 1;
                this.loadUsers();
            }, 500));
        }

        const roleFilter = document.getElementById('user-role-filter');
        if (roleFilter) {
            roleFilter.addEventListener('change', () => {
                this.currentFilters.role = roleFilter.value;
                this.currentFilters.page = 1;
                this.loadUsers();
            });
        }

        const statusFilter = document.getElementById('user-status-filter');
        if (statusFilter) {
            statusFilter.addEventListener('change', () => {
                this.currentFilters.status = statusFilter.value;
                this.currentFilters.page = 1;
                this.loadUsers();
            });
        }
    },

    async loadUsers() {
        try {
            Loading.show();

            // Mock data for now - replace with real API call
            const mockUsers = [
                {
                    _id: '1',
                    name: 'Nguyễn Văn A',
                    email: '<EMAIL>',
                    phone: '0123456789',
                    role: 'user',
                    isActive: true,
                    lastLogin: new Date(),
                    createdAt: new Date('2024-01-15'),
                    orderCount: 15,
                    totalSpent: 2500000
                },
                {
                    _id: '2',
                    name: 'Trần Thị B',
                    email: '<EMAIL>',
                    phone: '0987654321',
                    role: 'staff',
                    isActive: true,
                    lastLogin: new Date(),
                    createdAt: new Date('2024-02-20'),
                    orderCount: 0,
                    totalSpent: 0
                },
                {
                    _id: '3',
                    name: 'Administrator',
                    email: '<EMAIL>',
                    phone: '0999888777',
                    role: 'admin',
                    isActive: true,
                    lastLogin: new Date(),
                    createdAt: new Date('2024-01-01'),
                    orderCount: 0,
                    totalSpent: 0
                }
            ];

            this.users = mockUsers;
            this.renderUsersTable({ data: mockUsers, total: mockUsers.length });

        } catch (error) {
            console.error('Load users error:', error);
            Toast.error('Không thể tải danh sách người dùng');
        } finally {
            Loading.hide();
        }
    },

    renderUsersTable(response) {
        const container = document.getElementById('users-table');
        if (!container) return;

        if (this.users.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-users"></i>
                    <h3>Chưa có người dùng nào</h3>
                    <p>Danh sách người dùng sẽ hiển thị ở đây</p>
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Tên</th>
                        <th>Email</th>
                        <th>Điện thoại</th>
                        <th>Vai trò</th>
                        <th>Trạng thái</th>
                        <th>Đơn hàng</th>
                        <th>Tổng chi tiêu</th>
                        <th>Đăng ký</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    ${this.users.map(user => `
                        <tr>
                            <td>
                                <div class="user-info">
                                    <strong>${user.name}</strong>
                                    ${user.lastLogin ? `<small>Hoạt động: ${Utils.formatDate(user.lastLogin)}</small>` : ''}
                                </div>
                            </td>
                            <td>${user.email}</td>
                            <td>${user.phone || '-'}</td>
                            <td>
                                <span class="role-badge role-${user.role}">
                                    ${this.getRoleText(user.role)}
                                </span>
                            </td>
                            <td>
                                <span class="status-badge ${user.isActive ? 'status-active' : 'status-inactive'}">
                                    ${user.isActive ? 'Hoạt động' : 'Bị khóa'}
                                </span>
                            </td>
                            <td>${user.orderCount || 0}</td>
                            <td>${Utils.formatCurrency(user.totalSpent || 0)}</td>
                            <td>${Utils.formatDate(user.createdAt)}</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-action btn-edit" onclick="AdminUser.editUser('${user._id}')" title="Sửa">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-action btn-toggle" onclick="AdminUser.toggleUser('${user._id}')" title="${user.isActive ? 'Khóa' : 'Mở khóa'}">
                                        <i class="fas fa-${user.isActive ? 'lock' : 'unlock'}"></i>
                                    </button>
                                    ${user.role !== 'admin' ? `
                                        <button class="btn-action btn-delete" onclick="AdminUser.deleteUser('${user._id}')" title="Xóa">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    ` : ''}
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    },

    async editUser(userId) {
        const user = this.users.find(u => u._id === userId);
        if (!user) return;

        const modalContent = `
            <div class="user-modal">
                <div class="modal-header">
                    <h2>Sửa thông tin người dùng</h2>
                    <button class="modal-close" onclick="Modal.hide()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="user-form" class="user-form">
                        <div class="form-group">
                            <label>Tên *</label>
                            <input type="text" name="name" value="${user.name}" required>
                        </div>
                        
                        <div class="form-group">
                            <label>Email *</label>
                            <input type="email" name="email" value="${user.email}" required disabled>
                        </div>
                        
                        <div class="form-group">
                            <label>Điện thoại</label>
                            <input type="tel" name="phone" value="${user.phone || ''}">
                        </div>
                        
                        <div class="form-group">
                            <label>Vai trò *</label>
                            <select name="role" required ${user.role === 'admin' ? 'disabled' : ''}>
                                <option value="user" ${user.role === 'user' ? 'selected' : ''}>Khách hàng</option>
                                <option value="staff" ${user.role === 'staff' ? 'selected' : ''}>Nhân viên</option>
                                <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>Quản trị viên</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="isActive" ${user.isActive ? 'checked' : ''} ${user.role === 'admin' ? 'disabled' : ''}>
                                Tài khoản hoạt động
                            </label>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Cập nhật</button>
                            <button type="button" class="btn btn-secondary" onclick="Modal.hide()">Hủy</button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        Modal.show(modalContent);

        // Bind form submit
        document.getElementById('user-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveUser(e.target, userId);
        });
    },

    async saveUser(form, userId) {
        try {
            Loading.show();

            const formData = new FormData(form);
            const userData = {
                name: formData.get('name'),
                phone: formData.get('phone'),
                role: formData.get('role'),
                isActive: formData.has('isActive')
            };

            // Mock update - replace with real API call
            const userIndex = this.users.findIndex(u => u._id === userId);
            if (userIndex !== -1) {
                this.users[userIndex] = { ...this.users[userIndex], ...userData };
            }

            Modal.hide();
            this.renderUsersTable({ data: this.users, total: this.users.length });
            Toast.success('Cập nhật thông tin người dùng thành công!');

        } catch (error) {
            console.error('Save user error:', error);
            Toast.error('Có lỗi xảy ra: ' + error.message);
        } finally {
            Loading.hide();
        }
    },

    async toggleUser(userId) {
        const user = this.users.find(u => u._id === userId);
        if (!user || user.role === 'admin') return;

        try {
            // Mock toggle - replace with real API call
            user.isActive = !user.isActive;
            
            this.renderUsersTable({ data: this.users, total: this.users.length });
            Toast.success(`${user.isActive ? 'Mở khóa' : 'Khóa'} tài khoản thành công!`);

        } catch (error) {
            console.error('Toggle user error:', error);
            Toast.error('Có lỗi xảy ra khi cập nhật trạng thái');
        }
    },

    async deleteUser(userId) {
        const user = this.users.find(u => u._id === userId);
        if (!user || user.role === 'admin') return;

        if (!confirm(`Bạn có chắc chắn muốn xóa người dùng "${user.name}"?`)) {
            return;
        }

        try {
            Loading.show();

            // Mock delete - replace with real API call
            this.users = this.users.filter(u => u._id !== userId);
            
            this.renderUsersTable({ data: this.users, total: this.users.length });
            Toast.success('Xóa người dùng thành công!');

        } catch (error) {
            console.error('Delete user error:', error);
            Toast.error('Có lỗi xảy ra khi xóa người dùng');
        } finally {
            Loading.hide();
        }
    },

    getRoleText(role) {
        const roleMap = {
            'user': 'Khách hàng',
            'staff': 'Nhân viên',
            'admin': 'Quản trị viên'
        };
        return roleMap[role] || role;
    }
};

// Export for global use
window.AdminUser = AdminUser;
