// Product Detail Page Module
const ProductDetail = {
    currentProduct: null,
    selectedQuantity: 1,
    selectedVariant: null,

    init() {
        this.bindEvents();
        this.loadProductFromURL();
    },

    bindEvents() {
        // Quantity controls
        const quantityMinus = document.getElementById('quantity-minus');
        const quantityPlus = document.getElementById('quantity-plus');
        const quantityInput = document.getElementById('quantity-input');

        if (quantityMinus) {
            quantityMinus.addEventListener('click', () => {
                this.updateQuantity(-1);
            });
        }

        if (quantityPlus) {
            quantityPlus.addEventListener('click', () => {
                this.updateQuantity(1);
            });
        }

        if (quantityInput) {
            quantityInput.addEventListener('change', (e) => {
                this.setQuantity(parseInt(e.target.value) || 1);
            });
        }

        // Add to cart button
        const addToCartBtn = document.getElementById('add-to-cart-btn');
        if (addToCartBtn) {
            addToCartBtn.addEventListener('click', () => {
                this.addToCart();
            });
        }

        // Buy now button
        const buyNowBtn = document.getElementById('buy-now-btn');
        if (buyNowBtn) {
            buyNowBtn.addEventListener('click', () => {
                this.buyNow();
            });
        }

        // Image gallery
        this.bindImageGallery();

        // Variant selection
        this.bindVariantSelection();

        // Review form
        this.bindReviewForm();
    },

    loadProductFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        const productId = urlParams.get('id');
        
        if (productId) {
            this.loadProduct(productId);
        } else {
            // For SPA, check if we're on product detail view
            if (App.currentView === 'product-detail') {
                this.showError('Không tìm thấy sản phẩm');
            }
        }
    },

    async loadProduct(productId) {
        try {
            Loading.show();

            const response = await API.get(`/products/${productId}`);
            this.currentProduct = response.data;

            this.renderProduct();
            this.loadReviews();

        } catch (error) {
            console.error('Load product error:', error);
            this.showError('Không thể tải thông tin sản phẩm');
        } finally {
            Loading.hide();
        }
    },

    renderProduct() {
        if (!this.currentProduct) return;

        const product = this.currentProduct;

        // Update page title
        document.title = `${product.name} - Na Food`;

        // Product images
        this.renderImageGallery(product.images);

        // Product info
        this.renderProductInfo(product);

        // Product description
        this.renderProductDescription(product);

        // Related products
        this.loadRelatedProducts(product.category);
    },

    renderImageGallery(images) {
        const mainImage = document.getElementById('main-product-image');
        const thumbnails = document.getElementById('image-thumbnails');

        if (!images || images.length === 0) {
            if (mainImage) {
                mainImage.src = '/images/placeholder.jpg';
                mainImage.alt = 'Product image';
            }
            return;
        }

        // Main image
        if (mainImage) {
            mainImage.src = images[0];
            mainImage.alt = this.currentProduct.name;
        }

        // Thumbnails
        if (thumbnails) {
            thumbnails.innerHTML = images.map((image, index) => `
                <img src="${image}" 
                     alt="Product image ${index + 1}"
                     class="thumbnail ${index === 0 ? 'active' : ''}"
                     onclick="ProductDetail.selectImage(${index})">
            `).join('');
        }
    },

    selectImage(index) {
        const mainImage = document.getElementById('main-product-image');
        const thumbnails = document.querySelectorAll('.thumbnail');

        if (mainImage && this.currentProduct.images[index]) {
            mainImage.src = this.currentProduct.images[index];
        }

        thumbnails.forEach((thumb, i) => {
            thumb.classList.toggle('active', i === index);
        });
    },

    renderProductInfo(product) {
        // Product name
        const nameElement = document.getElementById('product-name');
        if (nameElement) {
            nameElement.textContent = product.name;
        }

        // Product price
        const priceElement = document.getElementById('product-price');
        if (priceElement) {
            if (product.salePrice && product.salePrice < product.price) {
                priceElement.innerHTML = `
                    <span class="sale-price">${Utils.formatCurrency(product.salePrice)}</span>
                    <span class="original-price">${Utils.formatCurrency(product.price)}</span>
                    <span class="discount-badge">-${Math.round((1 - product.salePrice / product.price) * 100)}%</span>
                `;
            } else {
                priceElement.innerHTML = `<span class="current-price">${Utils.formatCurrency(product.price)}</span>`;
            }
        }

        // Product rating
        const ratingElement = document.getElementById('product-rating');
        if (ratingElement) {
            ratingElement.innerHTML = `
                <div class="stars">${this.renderStars(product.averageRating || 0)}</div>
                <span class="rating-text">${product.averageRating || 0}/5 (${product.reviewCount || 0} đánh giá)</span>
            `;
        }

        // Product category
        const categoryElement = document.getElementById('product-category');
        if (categoryElement) {
            categoryElement.textContent = product.category;
        }

        // Product availability
        const availabilityElement = document.getElementById('product-availability');
        if (availabilityElement) {
            if (product.isAvailable) {
                availabilityElement.innerHTML = '<span class="in-stock">✓ Còn hàng</span>';
            } else {
                availabilityElement.innerHTML = '<span class="out-of-stock">✗ Hết hàng</span>';
            }
        }
    },

    renderProductDescription(product) {
        const descriptionElement = document.getElementById('product-description');
        if (descriptionElement) {
            descriptionElement.innerHTML = product.description || 'Chưa có mô tả cho sản phẩm này.';
        }

        // Ingredients
        const ingredientsElement = document.getElementById('product-ingredients');
        if (ingredientsElement && product.ingredients) {
            ingredientsElement.innerHTML = product.ingredients.join(', ');
        }

        // Nutrition info
        const nutritionElement = document.getElementById('nutrition-info');
        if (nutritionElement && product.nutrition) {
            nutritionElement.innerHTML = `
                <div class="nutrition-item">
                    <span>Calories:</span>
                    <span>${product.nutrition.calories || 'N/A'}</span>
                </div>
                <div class="nutrition-item">
                    <span>Protein:</span>
                    <span>${product.nutrition.protein || 'N/A'}g</span>
                </div>
                <div class="nutrition-item">
                    <span>Carbs:</span>
                    <span>${product.nutrition.carbs || 'N/A'}g</span>
                </div>
                <div class="nutrition-item">
                    <span>Fat:</span>
                    <span>${product.nutrition.fat || 'N/A'}g</span>
                </div>
            `;
        }
    },

    updateQuantity(change) {
        this.selectedQuantity = Math.max(1, this.selectedQuantity + change);
        this.updateQuantityDisplay();
    },

    setQuantity(quantity) {
        this.selectedQuantity = Math.max(1, quantity);
        this.updateQuantityDisplay();
    },

    updateQuantityDisplay() {
        const quantityInput = document.getElementById('quantity-input');
        if (quantityInput) {
            quantityInput.value = this.selectedQuantity;
        }

        // Update total price if variant is selected
        this.updateTotalPrice();
    },

    updateTotalPrice() {
        const totalPriceElement = document.getElementById('total-price');
        if (totalPriceElement && this.currentProduct) {
            const price = this.currentProduct.salePrice || this.currentProduct.price;
            const total = price * this.selectedQuantity;
            totalPriceElement.textContent = Utils.formatCurrency(total);
        }
    },

    bindImageGallery() {
        // Image zoom functionality
        const mainImage = document.getElementById('main-product-image');
        if (mainImage) {
            mainImage.addEventListener('click', () => {
                this.openImageModal(mainImage.src);
            });
        }
    },

    openImageModal(imageSrc) {
        const modalContent = `
            <div class="image-modal">
                <div class="modal-header">
                    <h3>Hình ảnh sản phẩm</h3>
                    <button class="modal-close" onclick="Modal.hide()">&times;</button>
                </div>
                <div class="modal-body">
                    <img src="${imageSrc}" alt="Product image" class="modal-image">
                </div>
            </div>
        `;
        Modal.show(modalContent);
    },

    bindVariantSelection() {
        const variantOptions = document.querySelectorAll('.variant-option');
        variantOptions.forEach(option => {
            option.addEventListener('click', () => {
                this.selectVariant(option.dataset.variant);
            });
        });
    },

    selectVariant(variantId) {
        // Update selected variant
        this.selectedVariant = variantId;

        // Update UI
        document.querySelectorAll('.variant-option').forEach(option => {
            option.classList.toggle('selected', option.dataset.variant === variantId);
        });

        // Update price if variant has different price
        this.updateTotalPrice();
    },

    async addToCart() {
        if (!this.currentProduct) return;

        try {
            const cartItem = {
                productId: this.currentProduct._id,
                name: this.currentProduct.name,
                price: this.currentProduct.salePrice || this.currentProduct.price,
                image: this.currentProduct.images?.[0] || '/images/placeholder.jpg',
                quantity: this.selectedQuantity,
                variant: this.selectedVariant
            };

            Cart.addItem(cartItem);
            Toast.success(`Đã thêm ${this.currentProduct.name} vào giỏ hàng!`);

            // Update cart UI
            Header.updateCartCount();

        } catch (error) {
            console.error('Add to cart error:', error);
            Toast.error('Có lỗi xảy ra khi thêm vào giỏ hàng');
        }
    },

    async buyNow() {
        await this.addToCart();
        // Navigate to checkout in SPA
        App.showView('checkout');
    },

    renderStars(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

        let starsHTML = '';
        
        for (let i = 0; i < fullStars; i++) {
            starsHTML += '<i class="fas fa-star"></i>';
        }
        
        if (hasHalfStar) {
            starsHTML += '<i class="fas fa-star-half-alt"></i>';
        }
        
        for (let i = 0; i < emptyStars; i++) {
            starsHTML += '<i class="far fa-star"></i>';
        }

        return starsHTML;
    },

    showError(message) {
        const container = document.getElementById('product-detail-container');
        if (container) {
            container.innerHTML = `
                <div class="error-state">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>Có lỗi xảy ra</h3>
                    <p>${message}</p>
                    <button onclick="App.showView('home')" class="btn btn-primary">Về trang chủ</button>
                </div>
            `;
        }
    }
};

// Export for global use
window.ProductDetail = ProductDetail;
