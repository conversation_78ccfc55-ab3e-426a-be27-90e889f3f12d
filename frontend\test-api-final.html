<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final API Test - Na Food</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        button {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-weight: 600;
        }
        button:hover { background: #e55a2b; }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .product-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
        }
        .product-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .product-name {
            font-weight: 600;
            margin-bottom: 5px;
            color: #333;
        }
        .product-price {
            color: #ff6b35;
            font-weight: 600;
            font-size: 1.1em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🍜 Na Food - Final API Test</h1>
        <p>Comprehensive test of all API endpoints and functionality</p>
        
        <div class="test-section info">
            <h2>📡 Server Status</h2>
            <div id="server-status">
                <span class="status-indicator status-offline"></span>
                Checking server connection...
            </div>
            <button onclick="checkServerStatus()">Refresh Status</button>
        </div>
        
        <div class="test-section">
            <h2>🔧 API Health Check</h2>
            <button onclick="testHealthAPI()">Test Health API</button>
            <div id="health-result"></div>
        </div>
        
        <div class="test-section">
            <h2>🍽️ Products API Test</h2>
            <button onclick="testProductsAPI()">Load Products</button>
            <button onclick="testProductsWithParams()">Load with Params</button>
            <div id="products-result"></div>
            <div id="products-display" class="product-grid"></div>
        </div>
        
        <div class="test-section">
            <h2>🔐 Authentication Test</h2>
            <button onclick="testLogin()">Test Login</button>
            <button onclick="testRegister()">Test Register</button>
            <div id="auth-result"></div>
        </div>
        
        <div class="test-section">
            <h2>📊 Database Test</h2>
            <button onclick="testDatabase()">Check Database</button>
            <div id="db-result"></div>
        </div>
        
        <div style="margin-top: 30px; text-align: center;">
            <a href="/" style="color: #ff6b35; text-decoration: none; font-weight: 600;">← Back to Main Site</a>
        </div>
    </div>

    <script>
        let serverOnline = false;

        async function checkServerStatus() {
            const statusDiv = document.getElementById('server-status');
            try {
                const response = await fetch('/api/health');
                if (response.ok) {
                    serverOnline = true;
                    statusDiv.innerHTML = '<span class="status-indicator status-online"></span>Server Online ✅';
                } else {
                    serverOnline = false;
                    statusDiv.innerHTML = '<span class="status-indicator status-offline"></span>Server Error ❌';
                }
            } catch (error) {
                serverOnline = false;
                statusDiv.innerHTML = '<span class="status-indicator status-offline"></span>Server Offline ❌';
            }
        }

        async function testHealthAPI() {
            const resultDiv = document.getElementById('health-result');
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Health API Working</h4>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Health API Error</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Health API Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testProductsAPI() {
            const resultDiv = document.getElementById('products-result');
            const displayDiv = document.getElementById('products-display');
            
            try {
                console.log('Making request to /api/products');
                const response = await fetch('/api/products');
                const data = await response.json();
                
                if (response.ok) {
                    const products = data.data || [];
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Products API Working</h4>
                            <p>Found ${products.length} products</p>
                            <p>Total: ${data.total || 0}</p>
                        </div>
                    `;
                    
                    // Display products
                    displayDiv.innerHTML = products.map(product => `
                        <div class="product-card">
                            <img src="${product.images[0]?.url || '/images/placeholder.jpg'}" 
                                 alt="${product.name}" class="product-image">
                            <div class="product-name">${product.name}</div>
                            <div class="product-price">${formatCurrency(product.price)}</div>
                        </div>
                    `).join('');
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Products API Error</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Products API Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testProductsWithParams() {
            const resultDiv = document.getElementById('products-result');
            
            try {
                const url = '/api/products?sort=-createdAt&page=1&limit=6';
                console.log('Making request to:', url);
                const response = await fetch(url);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Products API with Params Working</h4>
                            <p>URL: ${url}</p>
                            <p>Found ${data.data?.length || 0} products</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Products API with Params Error</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Products API with Params Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('auth-result');
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: '123456'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Login API Working</h4>
                            <p>User: ${data.user.name}</p>
                            <p>Email: ${data.user.email}</p>
                            <p>Token: ${data.token.substring(0, 20)}...</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Login API Error</h4>
                            <p>Status: ${response.status}</p>
                            <p>Error: ${data.error}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Login API Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testRegister() {
            const resultDiv = document.getElementById('auth-result');
            
            try {
                const testEmail = `test${Date.now()}@test.com`;
                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: 'Test User',
                        email: testEmail,
                        password: '123456'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Register API Working</h4>
                            <p>User: ${data.user.name}</p>
                            <p>Email: ${data.user.email}</p>
                            <p>Token: ${data.token.substring(0, 20)}...</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Register API Error</h4>
                            <p>Status: ${response.status}</p>
                            <p>Error: ${data.error}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Register API Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testDatabase() {
            const resultDiv = document.getElementById('db-result');
            
            try {
                // Test multiple endpoints to check database
                const [healthRes, productsRes] = await Promise.all([
                    fetch('/api/health'),
                    fetch('/api/products?limit=1')
                ]);
                
                const healthData = await healthRes.json();
                const productsData = await productsRes.json();
                
                if (healthRes.ok && productsRes.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Database Connection Working</h4>
                            <p>Health Status: ${healthData.message}</p>
                            <p>Products Count: ${productsData.total || 0}</p>
                            <p>Environment: ${healthData.environment}</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Database Connection Issues</h4>
                            <p>Health: ${healthRes.status}</p>
                            <p>Products: ${productsRes.status}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Database Test Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
            }).format(amount);
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            console.log('🚀 Final API test page loaded');
            checkServerStatus();
            setTimeout(testHealthAPI, 500);
            setTimeout(testProductsAPI, 1000);
        });
    </script>
</body>
</html>
